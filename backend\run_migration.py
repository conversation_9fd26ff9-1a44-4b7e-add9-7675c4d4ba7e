#!/usr/bin/env python3
"""
运行飞书支持的数据库迁移
"""

import os
import sys
import sqlite3
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def run_migration():
    """运行数据库迁移"""
    
    # 数据库文件路径
    db_path = project_root / "reddit_story_generator.db"
    
    if not db_path.exists():
        print(f"数据库文件不存在: {db_path}")
        return False
    
    print(f"连接数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        print("开始执行飞书支持迁移...")
        
        # 1. 检查settings表是否存在飞书字段
        cursor.execute("PRAGMA table_info(settings)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'feishu_app_id' not in columns:
            print("添加飞书配置字段到settings表...")
            cursor.execute("ALTER TABLE settings ADD COLUMN feishu_app_id VARCHAR(100)")
            cursor.execute("ALTER TABLE settings ADD COLUMN feishu_app_secret TEXT")
            cursor.execute("ALTER TABLE settings ADD COLUMN feishu_app_token VARCHAR(100)")
            cursor.execute("ALTER TABLE settings ADD COLUMN feishu_table_id VARCHAR(100)")
            print("✓ 飞书配置字段添加完成")
        else:
            print("✓ 飞书配置字段已存在")
        
        # 2. 检查video_generation_tasks表是否存在飞书字段
        cursor.execute("PRAGMA table_info(video_generation_tasks)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'feishu_record_id' not in columns:
            print("添加飞书相关字段到video_generation_tasks表...")
            cursor.execute("ALTER TABLE video_generation_tasks ADD COLUMN feishu_record_id VARCHAR(100)")
            cursor.execute("ALTER TABLE video_generation_tasks ADD COLUMN data_source VARCHAR(20) DEFAULT 'excel'")
            print("✓ 飞书任务字段添加完成")
        else:
            print("✓ 飞书任务字段已存在")
        
        # 3. 更新现有记录的data_source字段
        cursor.execute("UPDATE video_generation_tasks SET data_source = 'excel' WHERE data_source IS NULL")
        
        # 4. 创建索引
        try:
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_video_generation_tasks_feishu_record_id ON video_generation_tasks(feishu_record_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_video_generation_tasks_data_source ON video_generation_tasks(data_source)")
            print("✓ 索引创建完成")
        except Exception as e:
            print(f"索引创建警告: {e}")
        
        # 提交更改
        conn.commit()
        print("✓ 数据库迁移完成")
        
        return True
        
    except Exception as e:
        print(f"迁移失败: {e}")
        if conn:
            conn.rollback()
        return False
        
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    success = run_migration()
    if success:
        print("\n🎉 飞书支持迁移成功完成！")
        sys.exit(0)
    else:
        print("\n❌ 迁移失败")
        sys.exit(1)
