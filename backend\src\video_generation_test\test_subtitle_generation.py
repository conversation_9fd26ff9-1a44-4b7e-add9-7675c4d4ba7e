"""
字幕生成测试脚本

此脚本用于测试通过语音文件生成字幕的功能。
使用Whisper模型分析音频文件，提取时间戳，生成SRT字幕文件。

使用方法：
1. 准备测试音频文件（MP3格式）
2. 运行脚本：python -m backend.src.video_generation_test.test_subtitle_generation
3. 检查生成的字幕文件

注意：
- 需要安装openai-whisper库：pip install openai-whisper
- 首次运行会下载Whisper模型，需要网络连接
- 音频文件应包含清晰的中文语音
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_service import AudioAnalysisService, SubtitleGenerator
from src.schemas.video_generation import AudioAnalysis, WordTimestamp


class SubtitleGenerationTester:
    """字幕生成测试器"""
    
    def __init__(self):
        self.test_output_dir = Path("backend/src/video_generation_test/output/subtitles")
        self.test_output_dir.mkdir(parents=True, exist_ok=True)
        
        # 测试音频目录
        self.test_audio_dir = Path("backend/src/video_generation_test/output/audio")
        
    def create_sample_audio_analysis(self) -> AudioAnalysis:
        """创建示例音频分析数据（用于测试字幕生成逻辑）"""
        # 模拟Whisper分析结果
        word_timestamps = [
            WordTimestamp(word="这是", start=0.0, end=0.5),
            WordTimestamp(word="一个", start=0.5, end=0.8),
            WordTimestamp(word="测试", start=0.8, end=1.2),
            WordTimestamp(word="语音", start=1.2, end=1.6),
            WordTimestamp(word="生成", start=1.6, end=2.0),
            WordTimestamp(word="的", start=2.0, end=2.2),
            WordTimestamp(word="文本", start=2.2, end=2.6),
            WordTimestamp(word="。", start=2.6, end=2.8),
            WordTimestamp(word="今天", start=3.0, end=3.4),
            WordTimestamp(word="天气", start=3.4, end=3.8),
            WordTimestamp(word="很", start=3.8, end=4.0),
            WordTimestamp(word="好", start=4.0, end=4.3),
            WordTimestamp(word="，", start=4.3, end=4.4),
            WordTimestamp(word="适合", start=4.5, end=4.9),
            WordTimestamp(word="出去", start=4.9, end=5.3),
            WordTimestamp(word="走走", start=5.3, end=5.7),
            WordTimestamp(word="。", start=5.7, end=5.9),
        ]
        
        return AudioAnalysis(
            total_duration=6.0,
            first_sentence_duration=2.8,
            word_timestamps=word_timestamps
        )
    
    async def test_whisper_audio_analysis(self):
        """测试Whisper音频分析功能"""
        logger.info("=== 开始测试Whisper音频分析功能 ===")
        
        # 查找测试音频文件
        audio_files = list(self.test_audio_dir.glob("*.wav"))
        if not audio_files:
            logger.warning("⚠️ 未找到测试音频文件，请先运行语音生成测试")
            logger.info("💡 将使用模拟数据进行字幕生成测试")
            return None
        
        # 使用第一个音频文件进行测试
        test_audio_file = audio_files[0]
        logger.info(f"📁 使用测试音频文件: {test_audio_file}")
        
        # 对应的测试文本
        story_text = "What's your best proved the bully wrong comeback story? In our Monday standup, my manager smirked, Let the adults handle the spreadsheet, sweetheart. He’d cropped the Y‑axis and slapped a rosy filter on Q3 to impress the VP. I double‑tapped my Apple Watch and the projector flipped to the live dashboard I built—timestamps, audit trail, receipts. Silence. I said, Adults don’t hide losses in cosmetics, Brad. The VP slid my way the client lead—and slid him a performance plan. His monogrammed mug suddenly looked very small."
        speech_speed = 1.0
        
        try:
            # 调用Whisper分析
            logger.info("🔍 开始Whisper音频分析...")
            audio_analysis = await AudioAnalysisService.analyze_audio(
                str(test_audio_file), story_text, speech_speed
            )
            
            logger.info(f"✅ 音频分析完成:")
            logger.info(f"   总时长: {audio_analysis.total_duration:.2f}秒")
            logger.info(f"   第一句时长: {audio_analysis.first_sentence_duration:.2f}秒")
            logger.info(f"   词时间戳数量: {len(audio_analysis.word_timestamps)}")
            
            # 显示前几个词的时间戳
            if audio_analysis.word_timestamps:
                logger.info("📝 前5个词的时间戳:")
                for i, ts in enumerate(audio_analysis.word_timestamps[:5]):
                    logger.info(f"   {i+1}. '{ts.word}' [{ts.start:.2f}s - {ts.end:.2f}s]")
            
            return audio_analysis
            
        except Exception as e:
            logger.error(f"❌ Whisper音频分析失败: {str(e)}")
            logger.info("💡 将使用模拟数据进行字幕生成测试")
            return None
    
    def test_subtitle_generation_with_different_settings(self, audio_analysis: AudioAnalysis):
        """测试不同设置下的字幕生成"""
        logger.info("=== 开始测试字幕生成功能 ===")
        
        story_text = "这是一个测试语音生成的文本。今天天气很好，适合出去走走。"
        
        # 测试不同的字幕配置
        test_cases = [
            {
                "name": "每屏1个词",
                "words_per_screen": 1,
                "include_all_words": True
            },
            {
                "name": "每屏2个词",
                "words_per_screen": 2,
                "include_all_words": True
            },
            {
                "name": "每屏3个词",
                "words_per_screen": 3,
                "include_all_words": True
            },
            {
                "name": "跳过第一句话",
                "words_per_screen": 1,
                "include_all_words": False
            },
            {
                "name": "每屏5个词_包含所有",
                "words_per_screen": 5,
                "include_all_words": True
            }
        ]
        
        success_count = 0
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"\n--- 测试用例 {i}: {test_case['name']} ---")
            
            try:
                # 生成字幕
                srt_content = SubtitleGenerator.generate_srt(
                    word_timestamps=audio_analysis.word_timestamps,
                    story_text=story_text,
                    first_sentence_duration=audio_analysis.first_sentence_duration,
                    words_per_screen=test_case['words_per_screen'],
                    include_all_words=test_case['include_all_words']
                )
                
                if srt_content:
                    # 保存字幕文件
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"test_subtitle_{i}_{test_case['name']}_{timestamp}.srt"
                    output_path = self.test_output_dir / filename
                    
                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(srt_content)
                    
                    # 统计字幕段数
                    subtitle_count = srt_content.count('\n\n')
                    logger.info(f"✅ 字幕生成成功: {filename}")
                    logger.info(f"   字幕段数: {subtitle_count}")
                    logger.info(f"   文件大小: {len(srt_content)} 字符")
                    
                    # 显示前几行内容
                    lines = srt_content.split('\n')[:8]
                    logger.info("📝 字幕内容预览:")
                    for line in lines:
                        if line.strip():
                            logger.info(f"   {line}")
                    
                    success_count += 1
                else:
                    logger.error(f"❌ 字幕生成失败: {test_case['name']} - 内容为空")
                    
            except Exception as e:
                logger.error(f"❌ 字幕生成异常: {test_case['name']} - {str(e)}")
        
        logger.info(f"\n=== 字幕生成测试完成: {success_count}/{len(test_cases)} 个用例成功 ===")
        return success_count == len(test_cases)
    
    def test_srt_format_validation(self):
        """测试SRT格式验证"""
        logger.info("=== 开始测试SRT格式验证 ===")
        
        # 创建简单的测试数据
        word_timestamps = [
            WordTimestamp(word="Hello", start=0.0, end=1.0),
            WordTimestamp(word="World", start=1.0, end=2.0),
            WordTimestamp(word="!", start=2.0, end=2.5),
        ]
        
        audio_analysis = AudioAnalysis(
            total_duration=3.0,
            first_sentence_duration=2.5,
            word_timestamps=word_timestamps
        )
        
        try:
            srt_content = SubtitleGenerator.generate_srt(
                word_timestamps=audio_analysis.word_timestamps,
                story_text="Hello World!",
                first_sentence_duration=audio_analysis.first_sentence_duration,
                words_per_screen=1,
                include_all_words=True
            )
            
            # 验证SRT格式
            lines = srt_content.strip().split('\n')
            
            # 基本格式检查
            has_sequence = False
            has_timestamp = False
            has_text = False
            
            for line in lines:
                if line.isdigit():
                    has_sequence = True
                elif '-->' in line:
                    has_timestamp = True
                elif line.strip() and not line.isdigit() and '-->' not in line:
                    has_text = True
            
            if has_sequence and has_timestamp and has_text:
                logger.info("✅ SRT格式验证通过")
                logger.info("📝 SRT内容:")
                for line in lines:
                    logger.info(f"   {line}")
                return True
            else:
                logger.error("❌ SRT格式验证失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ SRT格式验证异常: {str(e)}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始字幕生成测试")
        logger.info(f"📁 测试输出目录: {self.test_output_dir.absolute()}")
        
        try:
            # Whisper音频分析测试
            audio_analysis = await self.test_whisper_audio_analysis()
            
            # 如果没有真实音频分析结果，使用模拟数据
            if audio_analysis is None:
                audio_analysis = self.create_sample_audio_analysis()
                logger.info("📝 使用模拟音频分析数据进行测试")
            
            # 字幕生成测试
            subtitle_success = self.test_subtitle_generation_with_different_settings(audio_analysis)
            
            # SRT格式验证测试
            format_success = self.test_srt_format_validation()
            
            # 总结
            logger.info("\n" + "="*50)
            logger.info("📊 测试结果总结:")
            logger.info(f"   字幕生成测试: {'✅ 通过' if subtitle_success else '❌ 失败'}")
            logger.info(f"   SRT格式验证: {'✅ 通过' if format_success else '❌ 失败'}")
            logger.info(f"📁 输出文件位置: {self.test_output_dir.absolute()}")
            logger.info("="*50)
            
            return subtitle_success and format_success
            
        except Exception as e:
            logger.error(f"❌ 测试执行异常: {str(e)}")
            return False


async def main():
    """主函数"""
    tester = SubtitleGenerationTester()
    success = await tester.run_all_tests()
    
    if success:
        logger.info("🎉 所有测试通过！")
        sys.exit(0)
    else:
        logger.error("💥 部分测试失败，请检查配置和日志")
        sys.exit(1)


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(sys.stdout, format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
    
    # 运行测试
    asyncio.run(main())
