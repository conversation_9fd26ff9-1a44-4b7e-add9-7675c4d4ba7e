"""
视频生成全流程测试脚本

此脚本按顺序运行所有视频生成环节的测试：
1. 语音生成测试
2. 字幕生成测试  
3. 封面生成测试
4. 视频合成测试

使用方法：
python -m backend.src.video_generation_test.run_all_tests

或者运行单个测试：
python -m backend.src.video_generation_test.run_all_tests --test audio
python -m backend.src.video_generation_test.run_all_tests --test subtitle
python -m backend.src.video_generation_test.run_all_tests --test cover
python -m backend.src.video_generation_test.run_all_tests --test video
"""

import asyncio
import sys
import argparse
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.src.video_generation_test.test_audio_generation import AudioGenerationTester
from backend.src.video_generation_test.test_subtitle_generation import SubtitleGenerationTester
from backend.src.video_generation_test.test_cover_generation import CoverGenerationTester
from backend.src.video_generation_test.test_video_composition import VideoCompositionTester


class VideoGenerationTestSuite:
    """视频生成测试套件"""
    
    def __init__(self):
        self.test_output_dir = Path("backend/src/video_generation_test/output")
        self.test_output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化各个测试器
        self.audio_tester = AudioGenerationTester()
        self.subtitle_tester = SubtitleGenerationTester()
        self.cover_tester = CoverGenerationTester()
        self.video_tester = VideoCompositionTester()
    
    async def run_audio_tests(self):
        """运行语音生成测试"""
        logger.info("🎵 开始语音生成测试...")
        try:
            success = await self.audio_tester.run_all_tests()
            if success:
                logger.info("✅ 语音生成测试通过")
            else:
                logger.error("❌ 语音生成测试失败")
            return success
        except Exception as e:
            logger.error(f"❌ 语音生成测试异常: {str(e)}")
            return False
    
    async def run_subtitle_tests(self):
        """运行字幕生成测试"""
        logger.info("📝 开始字幕生成测试...")
        try:
            success = await self.subtitle_tester.run_all_tests()
            if success:
                logger.info("✅ 字幕生成测试通过")
            else:
                logger.error("❌ 字幕生成测试失败")
            return success
        except Exception as e:
            logger.error(f"❌ 字幕生成测试异常: {str(e)}")
            return False
    
    async def run_cover_tests(self):
        """运行封面生成测试"""
        logger.info("🖼️ 开始封面生成测试...")
        try:
            success = await self.cover_tester.run_all_tests()
            if success:
                logger.info("✅ 封面生成测试通过")
            else:
                logger.error("❌ 封面生成测试失败")
            return success
        except Exception as e:
            logger.error(f"❌ 封面生成测试异常: {str(e)}")
            return False
    
    async def run_video_tests(self):
        """运行视频合成测试"""
        logger.info("🎬 开始视频合成测试...")
        try:
            success = await self.video_tester.run_all_tests()
            if success:
                logger.info("✅ 视频合成测试通过")
            else:
                logger.error("❌ 视频合成测试失败")
            return success
        except Exception as e:
            logger.error(f"❌ 视频合成测试异常: {str(e)}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始视频生成全流程测试")
        logger.info(f"📁 测试输出目录: {self.test_output_dir.absolute()}")
        logger.info("="*60)
        
        results = {}
        
        # 按顺序运行测试
        logger.info("第1步: 语音生成测试")
        results['audio'] = await self.run_audio_tests()
        
        logger.info("\n第2步: 字幕生成测试")
        results['subtitle'] = await self.run_subtitle_tests()
        
        logger.info("\n第3步: 封面生成测试")
        results['cover'] = await self.run_cover_tests()
        
        logger.info("\n第4步: 视频合成测试")
        results['video'] = await self.run_video_tests()
        
        # 输出总结
        self.print_summary(results)
        
        # 返回是否所有测试都通过
        return all(results.values())
    
    def print_summary(self, results):
        """打印测试结果总结"""
        logger.info("\n" + "="*60)
        logger.info("📊 视频生成全流程测试结果总结")
        logger.info("="*60)
        
        test_names = {
            'audio': '语音生成',
            'subtitle': '字幕生成', 
            'cover': '封面生成',
            'video': '视频合成'
        }
        
        passed_count = 0
        for test_key, test_name in test_names.items():
            status = "✅ 通过" if results.get(test_key, False) else "❌ 失败"
            logger.info(f"   {test_name}: {status}")
            if results.get(test_key, False):
                passed_count += 1
        
        logger.info(f"\n总体结果: {passed_count}/{len(test_names)} 个测试通过")
        
        if passed_count == len(test_names):
            logger.info("🎉 所有测试通过！视频生成功能正常")
        elif passed_count > 0:
            logger.warning("⚠️ 部分测试通过，请检查失败的测试项")
        else:
            logger.error("💥 所有测试失败，请检查系统配置")
        
        logger.info(f"📁 测试输出文件位置: {self.test_output_dir.absolute()}")
        logger.info("="*60)
    
    async def run_single_test(self, test_type):
        """运行单个测试"""
        test_map = {
            'audio': self.run_audio_tests,
            'subtitle': self.run_subtitle_tests,
            'cover': self.run_cover_tests,
            'video': self.run_video_tests
        }
        
        if test_type not in test_map:
            logger.error(f"❌ 未知的测试类型: {test_type}")
            logger.info(f"可用的测试类型: {', '.join(test_map.keys())}")
            return False
        
        logger.info(f"🚀 运行单个测试: {test_type}")
        success = await test_map[test_type]()
        
        if success:
            logger.info(f"🎉 {test_type} 测试通过！")
        else:
            logger.error(f"💥 {test_type} 测试失败！")
        
        return success


def setup_logging():
    """设置日志格式"""
    logger.remove()
    logger.add(
        sys.stdout, 
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="视频生成测试套件")
    parser.add_argument(
        '--test', 
        choices=['audio', 'subtitle', 'cover', 'video'],
        help='运行指定的单个测试 (audio/subtitle/cover/video)'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细日志'
    )
    
    return parser.parse_args()


async def main():
    """主函数"""
    # 解析参数
    args = parse_arguments()
    
    # 设置日志
    setup_logging()
    if args.verbose:
        logger.remove()
        logger.add(sys.stdout, level="DEBUG")
    
    # 创建测试套件
    test_suite = VideoGenerationTestSuite()
    
    try:
        if args.test:
            # 运行单个测试
            success = await test_suite.run_single_test(args.test)
        else:
            # 运行所有测试
            success = await test_suite.run_all_tests()
        
        # 退出码
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.warning("⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 测试执行异常: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
