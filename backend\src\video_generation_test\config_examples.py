"""
视频生成测试配置示例

此文件包含各个测试环节的配置示例，可以作为参考来理解和修改测试参数。
"""

# ==================== TTS配置示例 ====================

# Coze TTS配置示例
COZE_TTS_CONFIG = {
    "provider": "coze",
    "apiKey": "your-coze-api-key",
    "model": "your-workflow-id",  # Coze工作流ID
    "endpoint": "https://api.coze.com"
}

# F5-TTS配置示例
F5_TTS_CONFIG = {
    "provider": "f5-tts",
    "f5TtsEndpoint": "http://localhost:8000",  # F5-TTS服务端点
    "model": "f5-tts"
}

# ==================== 语音生成测试参数 ====================

# 基本语音测试用例
AUDIO_TEST_CASES = [
    {
        "name": "标准语速测试",
        "voice": "zh-CN-XiaoxiaoNeural",
        "speed": 1.0,
        "text": "这是一个标准语速的测试文本。"
    },
    {
        "name": "快速语音测试",
        "voice": "zh-CN-XiaoxiaoNeural", 
        "speed": 1.5,
        "text": "这是一个快速语音的测试文本。"
    },
    {
        "name": "慢速语音测试",
        "voice": "zh-CN-XiaoxiaoNeural",
        "speed": 0.8,
        "text": "这是一个慢速语音的测试文本。"
    }
]

# 不同音色测试
VOICE_OPTIONS = [
    "zh-CN-XiaoxiaoNeural",  # 晓晓 - 女声
    "zh-CN-YunxiNeural",     # 云希 - 男声
    "zh-CN-XiaoyiNeural",    # 晓伊 - 女声
    "zh-CN-YunjianNeural",   # 云健 - 男声
    "zh-CN-XiaochenNeural",  # 晓辰 - 女声
    "zh-CN-XiaohanNeural",   # 晓涵 - 女声
]

# ==================== 字幕生成测试参数 ====================

# 字幕配置测试用例
SUBTITLE_TEST_CASES = [
    {
        "name": "每屏1个词",
        "words_per_screen": 1,
        "include_all_words": True
    },
    {
        "name": "每屏2个词",
        "words_per_screen": 2,
        "include_all_words": True
    },
    {
        "name": "每屏3个词",
        "words_per_screen": 3,
        "include_all_words": True
    },
    {
        "name": "跳过第一句话",
        "words_per_screen": 1,
        "include_all_words": False
    }
]

# 模拟音频分析数据
SAMPLE_WORD_TIMESTAMPS = [
    {"word": "这是", "start": 0.0, "end": 0.5},
    {"word": "一个", "start": 0.5, "end": 0.8},
    {"word": "测试", "start": 0.8, "end": 1.2},
    {"word": "语音", "start": 1.2, "end": 1.6},
    {"word": "生成", "start": 1.6, "end": 2.0},
    {"word": "的", "start": 2.0, "end": 2.2},
    {"word": "文本", "start": 2.2, "end": 2.6},
    {"word": "。", "start": 2.6, "end": 2.8},
]

# ==================== 封面生成测试参数 ====================

# 封面测试标题
COVER_TEST_TITLES = [
    "今天天气很好",
    "这是一个比较长的标题，用来测试封面生成时的文字排版和换行效果",
    "你知道吗？这个秘密让所有人都震惊了！",
    "Amazing Discovery That Will Change Everything",
    "AI技术的Amazing突破，你准备好了吗？",
    "🔥热门话题：最新科技趋势分析",
    "【重要】必看的生活小技巧合集"
]

# 封面模板配置示例
COVER_TEMPLATE_CONFIG = {
    "template_id": "default-template",
    "width": 1080,
    "height": 1920,
    "background_color": "#1a1a2e",
    "title_font_size": 60,
    "title_color": "#ffffff",
    "account_font_size": 40,
    "account_color": "#cccccc"
}

# ==================== 视频合成测试参数 ====================

# 视频设置配置
VIDEO_SETTINGS_CONFIGS = [
    {
        "name": "竖屏高清",
        "resolution": "1080x1920",
        "fps": 30,
        "format": "mp4"
    },
    {
        "name": "横屏高清", 
        "resolution": "1920x1080",
        "fps": 30,
        "format": "mp4"
    },
    {
        "name": "方形视频",
        "resolution": "1080x1080", 
        "fps": 30,
        "format": "mp4"
    }
]

# 音频设置配置
AUDIO_SETTINGS_CONFIGS = [
    {
        "name": "标准音频",
        "speech_volume": 1.0,
        "background_music_volume": 0.15,
        "enable_background_music": True
    },
    {
        "name": "无背景音乐",
        "speech_volume": 1.0,
        "background_music_volume": 0.0,
        "enable_background_music": False
    },
    {
        "name": "低背景音乐",
        "speech_volume": 1.0,
        "background_music_volume": 0.08,
        "enable_background_music": True
    }
]

# 字幕设置配置
SUBTITLE_SETTINGS_CONFIGS = [
    {
        "name": "标准白色字幕",
        "font_family": "Arial",
        "font_size": 24,
        "font_color": "#FFFFFF",
        "position": "bottom",
        "enabled": True
    },
    {
        "name": "大号黄色字幕",
        "font_family": "Arial",
        "font_size": 32,
        "font_color": "#FFFF00",
        "position": "bottom",
        "enabled": True
    },
    {
        "name": "无字幕",
        "enabled": False
    }
]

# 封面设置配置
COVER_SETTINGS_CONFIGS = [
    {
        "name": "居中显示",
        "position": "center",
        "animation": "fade_in_out",
        "animation_duration": 0.5
    },
    {
        "name": "左上角显示",
        "position": "top_left",
        "animation": "none",
        "custom_x": 50,
        "custom_y": 50
    },
    {
        "name": "右下角显示",
        "position": "bottom_right",
        "animation": "slide_in",
        "animation_duration": 1.0
    }
]

# ==================== 完整测试配置示例 ====================

# 完整的视频生成作业配置示例
COMPLETE_JOB_CONFIG = {
    "voice_settings": {
        "voice": "zh-CN-XiaoxiaoNeural",
        "male_voice": "zh-CN-YunxiNeural",
        "female_voice": "zh-CN-XiaoxiaoNeural",
        "speed": 1.0
    },
    "video_settings": {
        "resolution": "1080x1920",
        "fps": 30,
        "format": "mp4"
    },
    "audio_settings": {
        "speech_volume": 1.0,
        "background_music_volume": 0.15,
        "enable_background_music": True,
        "max_audio_duration": 60  # 最大音频时长（秒）
    },
    "subtitle_config": {
        "words_per_screen": 1,
        "include_all_words": True,
        "font_family": "Arial",
        "font_size": 24,
        "font_color": "#FFFFFF",
        "position": "bottom",
        "enabled": True
    },
    "cover_config": {
        "template_id": "default",
        "position": "center",
        "animation": "fade_in_out",
        "animation_duration": 0.5
    },
    "transition_config": {
        "type": "fade",
        "duration": 0.5
    }
}

# ==================== 测试数据示例 ====================

# 测试文本集合
TEST_TEXTS = {
    "short": "今天天气很好。",
    "medium": "这是一个中等长度的测试文本，用来验证语音生成的效果。",
    "long": """
    这是一个较长的测试文本，用来测试系统处理长文本的能力。
    文本包含多个句子，每个句子都有不同的内容和语调。
    通过这样的测试，我们可以验证TTS系统的稳定性和质量。
    同时也可以测试字幕生成和视频合成的完整流程。
    """,
    "with_punctuation": "你知道吗？这个发现真的很令人震惊！让我们一起来看看吧。",
    "mixed_language": "今天我们来学习AI技术，这是一个Amazing的领域！"
}

# 测试账号信息示例
TEST_ACCOUNT_INFO = {
    "name": "测试账号",
    "platform": "抖音",
    "description": "用于测试的虚拟账号"
}

# 测试模板信息示例
TEST_TEMPLATE_INFO = {
    "name": "默认模板",
    "description": "用于测试的默认封面模板",
    "width": 1080,
    "height": 1920
}
