@echo off
echo ========================================
echo Reddit Story Video Generator with Feishu
echo ========================================
echo.

echo 正在启动后端服务...
cd backend
start "Backend Server" cmd /k "python main.py"
cd ..

echo 等待后端启动...
timeout /t 5 /nobreak > nul

echo 正在启动前端服务...
cd frontend
start "Frontend Server" cmd /k "npm run dev"
cd ..

echo.
echo ========================================
echo 服务启动完成！
echo.
echo 后端服务: http://localhost:8000
echo 前端服务: http://localhost:3000
echo.
echo 飞书多维表格功能已集成：
echo 1. 访问设置页面配置飞书参数
echo 2. 在批量生成页面选择飞书导入
echo 3. 系统将自动回写生成状态
echo.
echo 详细使用说明请查看: 飞书多维表格集成使用说明.md
echo ========================================
pause
