"""
语音生成测试脚本

此脚本用于测试TTS服务的语音生成功能。
可以测试不同的TTS提供商（Coze、F5-TTS）和不同的语音参数。

使用方法：
1. 确保数据库中已配置TTS设置
2. 运行脚本：python -m backend.src.video_generation_test.test_audio_generation
3. 检查生成的音频文件

注意：
- 需要先在系统设置中配置TTS服务（Coze或F5-TTS）
- 确保相关的API密钥和端点已正确配置
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.src.services.tts_service import TTSService
from backend.src.database import get_session_maker
from backend.src.services.settings_service import get_current_tts_config


class AudioGenerationTester:
    """语音生成测试器"""
    
    def __init__(self):
        self.session_maker = get_session_maker()
        self.tts_service = TTSService(self.session_maker)
        self.test_output_dir = Path("backend/src/video_generation_test/output/audio")
        self.test_output_dir.mkdir(parents=True, exist_ok=True)
    
    async def test_basic_audio_generation(self):
        """测试基本语音生成功能"""
        logger.info("=== 开始测试基本语音生成功能 ===")
        
        # 测试文本
        test_text = "这是一个测试语音生成的文本。今天天气很好，适合出去走走。"
        
        # 测试参数
        test_cases = [
            {
                "name": "标准语速",
                "voice": "zh-CN-XiaoxiaoNeural",  # 默认音色，实际使用时会根据配置调整
                "speed": 1.0,
                "text": test_text
            },
            {
                "name": "快速语音",
                "voice": "zh-CN-XiaoxiaoNeural",
                "speed": 1.5,
                "text": test_text
            },
            {
                "name": "慢速语音",
                "voice": "zh-CN-XiaoxiaoNeural",
                "speed": 0.8,
                "text": test_text
            },
            {
                "name": "长文本测试",
                "voice": "zh-CN-XiaoxiaoNeural",
                "speed": 1.0,
                "text": """
                这是一个长文本测试。从前有一个小村庄，村庄里住着一位善良的老人。
                老人每天都会在村口等待远方归来的游子。有一天，一个年轻人来到了村庄，
                他告诉老人，他正在寻找传说中的宝藏。老人微笑着说，真正的宝藏就在你的心中。
                年轻人听了这话，若有所思地点了点头。
                """
            }
        ]
        
        # 检查TTS配置
        db = self.session_maker()
        try:
            tts_config = get_current_tts_config(db)
            if not tts_config:
                logger.error("❌ TTS配置未找到，请先在系统设置中配置TTS服务")
                return False
            
            logger.info(f"✅ 当前TTS配置: {tts_config.get('provider', 'unknown')}")
        finally:
            db.close()
        
        # 执行测试用例
        success_count = 0
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"\n--- 测试用例 {i}: {test_case['name']} ---")
            
            # 生成输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_audio_{i}_{test_case['name']}_{timestamp}.mp3"
            output_path = self.test_output_dir / filename
            
            try:
                # 调用TTS服务
                success = await self.tts_service.generate_speech(
                    text=test_case['text'],
                    voice=test_case['voice'],
                    speed=test_case['speed'],
                    output_path=str(output_path)
                )
                
                if success and output_path.exists():
                    file_size = output_path.stat().st_size
                    logger.info(f"✅ 语音生成成功: {filename} (大小: {file_size} bytes)")
                    success_count += 1
                else:
                    logger.error(f"❌ 语音生成失败: {test_case['name']}")
                    
            except Exception as e:
                logger.error(f"❌ 测试用例执行异常: {test_case['name']} - {str(e)}")
        
        logger.info(f"\n=== 测试完成: {success_count}/{len(test_cases)} 个用例成功 ===")
        return success_count == len(test_cases)
    
    async def test_different_voices(self):
        """测试不同音色（如果支持）"""
        logger.info("=== 开始测试不同音色 ===")
        
        # 常见的音色选项（实际可用音色取决于TTS提供商配置）
        voice_options = [
            "zh-CN-XiaoxiaoNeural",  # 女声
            "zh-CN-YunxiNeural",     # 男声
            "zh-CN-XiaoyiNeural",    # 女声
            "zh-CN-YunjianNeural",   # 男声
        ]
        
        test_text = "大家好，我是语音助手，很高兴为您服务。"
        
        success_count = 0
        for i, voice in enumerate(voice_options, 1):
            logger.info(f"\n--- 测试音色 {i}: {voice} ---")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_voice_{i}_{voice}_{timestamp}.mp3"
            output_path = self.test_output_dir / filename
            
            try:
                success = await self.tts_service.generate_speech(
                    text=test_text,
                    voice=voice,
                    speed=1.0,
                    output_path=str(output_path)
                )
                
                if success and output_path.exists():
                    logger.info(f"✅ 音色测试成功: {voice}")
                    success_count += 1
                else:
                    logger.warning(f"⚠️ 音色可能不支持: {voice}")
                    
            except Exception as e:
                logger.warning(f"⚠️ 音色测试异常: {voice} - {str(e)}")
        
        logger.info(f"\n=== 音色测试完成: {success_count}/{len(voice_options)} 个音色可用 ===")
        return success_count > 0
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始语音生成测试")
        logger.info(f"📁 测试输出目录: {self.test_output_dir.absolute()}")
        
        try:
            # 基本功能测试
            basic_success = await self.test_basic_audio_generation()
            
            # 音色测试
            voice_success = await self.test_different_voices()
            
            # 总结
            logger.info("\n" + "="*50)
            logger.info("📊 测试结果总结:")
            logger.info(f"   基本功能测试: {'✅ 通过' if basic_success else '❌ 失败'}")
            logger.info(f"   音色测试: {'✅ 通过' if voice_success else '❌ 失败'}")
            logger.info(f"📁 输出文件位置: {self.test_output_dir.absolute()}")
            logger.info("="*50)
            
            return basic_success and voice_success
            
        except Exception as e:
            logger.error(f"❌ 测试执行异常: {str(e)}")
            return False


async def main():
    """主函数"""
    tester = AudioGenerationTester()
    success = await tester.run_all_tests()
    
    if success:
        logger.info("🎉 所有测试通过！")
        sys.exit(0)
    else:
        logger.error("💥 部分测试失败，请检查配置和日志")
        sys.exit(1)


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(sys.stdout, format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
    
    # 运行测试
    asyncio.run(main())
