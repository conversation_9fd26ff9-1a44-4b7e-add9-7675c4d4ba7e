-- 添加飞书多维表格支持的数据库迁移
-- 执行时间: 2024-12-19

-- 1. 在settings表中添加飞书配置字段
ALTER TABLE settings ADD COLUMN feishu_app_id VARCHAR(100) COMMENT '飞书应用ID';
ALTER TABLE settings ADD COLUMN feishu_app_secret TEXT COMMENT '飞书应用密钥';
ALTER TABLE settings ADD COLUMN feishu_app_token VARCHAR(100) COMMENT '飞书多维表格App Token';
ALTER TABLE settings ADD COLUMN feishu_table_id VARCHAR(100) COMMENT '飞书多维表格Table ID';

-- 2. 在video_generation_tasks表中添加飞书相关字段
ALTER TABLE video_generation_tasks ADD COLUMN feishu_record_id VARCHAR(100) COMMENT '飞书多维表格记录ID';
ALTER TABLE video_generation_tasks ADD COLUMN data_source VARCHAR(20) DEFAULT 'excel' COMMENT '数据源类型: excel|feishu';

-- 3. 为新字段创建索引以提高查询性能
CREATE INDEX idx_video_generation_tasks_feishu_record_id ON video_generation_tasks(feishu_record_id);
CREATE INDEX idx_video_generation_tasks_data_source ON video_generation_tasks(data_source);

-- 4. 更新现有记录的data_source字段为默认值
UPDATE video_generation_tasks SET data_source = 'excel' WHERE data_source IS NULL;
