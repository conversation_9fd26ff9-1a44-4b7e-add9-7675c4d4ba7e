"""
视频合成测试脚本

此脚本用于测试视频合成功能。
将语音、字幕、封面、背景视频和背景音乐合成为最终视频。

使用方法：
1. 确保已有测试的语音、字幕、封面文件
2. 确保数据库中有视频素材和背景音乐
3. 运行脚本：python -m backend.src.video_generation_test.test_video_composition
4. 检查生成的视频文件

注意：
- 需要安装FFmpeg并添加到系统PATH
- 需要安装ffmpeg-python：pip install ffmpeg-python
- 确保数据库中有可用的视频素材和背景音乐
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.src.services.video_generation_helpers import VideoCompositionService
from backend.src.database import get_session_maker
from backend.src.models import VideoMaterial, BackgroundMusic
from backend.src.schemas.video_generation import AudioAnalysis, WordTimestamp


class VideoCompositionTester:
    """视频合成测试器"""
    
    def __init__(self):
        self.session_maker = get_session_maker()
        self.test_output_dir = Path("backend/src/video_generation_test/output/videos")
        self.test_output_dir.mkdir(parents=True, exist_ok=True)
        
        # 其他测试文件目录
        self.audio_dir = Path("backend/src/video_generation_test/output/audio")
        self.subtitle_dir = Path("backend/src/video_generation_test/output/subtitles")
        self.cover_dir = Path("backend/src/video_generation_test/output/covers")
    
    def get_test_materials(self):
        """获取测试所需的素材"""
        db = self.session_maker()
        try:
            # 获取视频素材
            materials = db.query(VideoMaterial).limit(3).all()
            if not materials:
                logger.error("❌ 数据库中没有找到视频素材")
                return None, None
            
            # 获取背景音乐
            background_music = db.query(BackgroundMusic).first()
            if not background_music:
                logger.error("❌ 数据库中没有找到背景音乐")
                return materials, None
            
            logger.info(f"✅ 找到 {len(materials)} 个视频素材")
            logger.info(f"✅ 找到背景音乐: {background_music.name}")
            
            return materials, background_music
            
        finally:
            db.close()
    
    def get_test_files(self):
        """获取测试文件路径"""
        # 查找测试文件
        audio_files = list(self.audio_dir.glob("*.mp3"))
        subtitle_files = list(self.subtitle_dir.glob("*.srt"))
        cover_files = list(self.cover_dir.glob("*.png"))
        
        audio_file = audio_files[0] if audio_files else None
        subtitle_file = subtitle_files[0] if subtitle_files else None
        cover_file = cover_files[0] if cover_files else None
        
        logger.info(f"📁 音频文件: {audio_file.name if audio_file else '未找到'}")
        logger.info(f"📁 字幕文件: {subtitle_file.name if subtitle_file else '未找到'}")
        logger.info(f"📁 封面文件: {cover_file.name if cover_file else '未找到'}")
        
        return audio_file, subtitle_file, cover_file
    
    def create_sample_audio_analysis(self, duration: float = 10.0) -> AudioAnalysis:
        """创建示例音频分析数据"""
        word_timestamps = [
            WordTimestamp(word="这是", start=0.0, end=0.5),
            WordTimestamp(word="一个", start=0.5, end=0.8),
            WordTimestamp(word="视频", start=0.8, end=1.2),
            WordTimestamp(word="合成", start=1.2, end=1.6),
            WordTimestamp(word="测试", start=1.6, end=2.0),
            WordTimestamp(word="。", start=2.0, end=2.2),
        ]
        
        return AudioAnalysis(
            total_duration=duration,
            first_sentence_duration=2.2,
            word_timestamps=word_timestamps
        )
    
    def create_fallback_files(self):
        """创建备用测试文件"""
        logger.info("📝 创建备用测试文件...")
        
        # 创建简单的SRT字幕文件
        fallback_subtitle = self.subtitle_dir / "fallback_test.srt"
        srt_content = """1
00:00:00,000 --> 00:00:02,000
这是一个视频合成测试

2
00:00:02,000 --> 00:00:04,000
用于验证视频生成功能

3
00:00:04,000 --> 00:00:06,000
请检查最终输出效果
"""
        with open(fallback_subtitle, 'w', encoding='utf-8') as f:
            f.write(srt_content)
        
        # 创建简单的封面图片
        fallback_cover = self.cover_dir / "fallback_test.png"
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # 创建1080x1920的封面
            width, height = 1080, 1920
            image = Image.new('RGB', (width, height), (26, 26, 46))
            draw = ImageDraw.Draw(image)
            
            # 添加文字
            try:
                font = ImageFont.truetype("arial.ttf", 60)
            except:
                font = ImageFont.load_default()
            
            text = "视频合成测试"
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (width - text_width) // 2
            y = (height - text_height) // 2
            
            draw.text((x, y), text, fill=(255, 255, 255), font=font)
            image.save(fallback_cover)
            
            logger.info(f"✅ 创建备用封面: {fallback_cover}")
            
        except Exception as e:
            logger.warning(f"⚠️ 创建备用封面失败: {e}")
            fallback_cover = None
        
        return None, fallback_subtitle, fallback_cover
    
    async def test_basic_video_composition(self):
        """测试基本视频合成功能"""
        logger.info("=== 开始测试基本视频合成功能 ===")
        
        # 获取测试素材
        materials, background_music = self.get_test_materials()
        if not materials or not background_music:
            logger.error("❌ 缺少测试素材，无法进行视频合成测试")
            return False
        
        # 获取测试文件
        audio_file, subtitle_file, cover_file = self.get_test_files()
        
        # 如果缺少文件，创建备用文件
        if not audio_file or not subtitle_file or not cover_file:
            logger.warning("⚠️ 部分测试文件缺失，创建备用文件")
            fallback_audio, fallback_subtitle, fallback_cover = self.create_fallback_files()
            
            if not audio_file:
                audio_file = fallback_audio
            if not subtitle_file:
                subtitle_file = fallback_subtitle
            if not cover_file:
                cover_file = fallback_cover
        
        # 如果仍然缺少关键文件，跳过测试
        if not subtitle_file or not cover_file:
            logger.error("❌ 缺少关键测试文件，无法进行视频合成测试")
            return False
        
        # 创建音频分析数据
        audio_analysis = self.create_sample_audio_analysis(10.0)
        
        # 测试用例
        test_cases = [
            {
                "name": "基本合成测试",
                "video_settings": {
                    'resolution': '1080x1920',
                    'fps': 30,
                    'format': 'mp4'
                },
                "audio_settings": {
                    'speech_volume': 1.0,
                    'background_music_volume': 0.15,
                    'enable_background_music': True
                },
                "subtitle_settings": {
                    'font_family': 'Arial',
                    'font_size': 24,
                    'font_color': '#FFFFFF',
                    'position': 'bottom',
                    'enabled': True
                }
            },
            {
                "name": "无背景音乐测试",
                "video_settings": {
                    'resolution': '1080x1920',
                    'fps': 30,
                    'format': 'mp4'
                },
                "audio_settings": {
                    'speech_volume': 1.0,
                    'background_music_volume': 0.0,
                    'enable_background_music': False
                },
                "subtitle_settings": {
                    'font_family': 'Arial',
                    'font_size': 28,
                    'font_color': '#FFFF00',
                    'position': 'bottom',
                    'enabled': True
                }
            }
        ]
        
        success_count = 0
        
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"\n--- 测试用例 {i}: {test_case['name']} ---")
            
            # 生成输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_video_{i}_{test_case['name']}_{timestamp}.mp4"
            output_path = self.test_output_dir / filename
            
            try:
                # 创建模拟任务对象
                class MockTask:
                    def __init__(self):
                        self.id = f"test_video_task_{i}"
                
                mock_task = MockTask()
                
                # 调用视频合成服务
                success = await VideoCompositionService.compose_video(
                    task=mock_task,
                    materials=materials,
                    background_music=background_music,
                    audio_duration=audio_analysis.total_duration,
                    cover_image_path=str(cover_file) if cover_file else "",
                    first_sentence_duration=audio_analysis.first_sentence_duration,
                    subtitle_file_path=str(subtitle_file) if subtitle_file else "",
                    output_path=str(output_path),
                    video_settings=test_case['video_settings'],
                    audio_settings=test_case['audio_settings'],
                    subtitle_settings=test_case['subtitle_settings']
                )
                
                if success and output_path.exists():
                    file_size = output_path.stat().st_size
                    logger.info(f"✅ 视频合成成功: {filename}")
                    logger.info(f"   文件大小: {file_size / (1024*1024):.2f} MB")
                    success_count += 1
                else:
                    logger.error(f"❌ 视频合成失败: {test_case['name']}")
                    
            except Exception as e:
                logger.error(f"❌ 测试用例执行异常: {test_case['name']} - {str(e)}")
        
        logger.info(f"\n=== 基本视频合成测试完成: {success_count}/{len(test_cases)} 个用例成功 ===")
        return success_count > 0
    
    async def test_different_resolutions(self):
        """测试不同分辨率的视频合成"""
        logger.info("=== 开始测试不同分辨率视频合成 ===")
        
        # 获取测试素材
        materials, background_music = self.get_test_materials()
        if not materials or not background_music:
            logger.error("❌ 缺少测试素材")
            return False
        
        # 获取测试文件
        _, subtitle_file, cover_file = self.get_test_files()
        if not subtitle_file or not cover_file:
            _, subtitle_file, cover_file = self.create_fallback_files()
        
        if not subtitle_file or not cover_file:
            logger.error("❌ 缺少测试文件")
            return False
        
        # 不同分辨率测试
        resolution_tests = [
            {"name": "竖屏_1080x1920", "resolution": "1080x1920"},
            {"name": "横屏_1920x1080", "resolution": "1920x1080"},
            {"name": "方形_1080x1080", "resolution": "1080x1080"},
        ]
        
        audio_analysis = self.create_sample_audio_analysis(8.0)
        success_count = 0
        
        for i, res_test in enumerate(resolution_tests, 1):
            logger.info(f"\n--- 分辨率测试 {i}: {res_test['name']} ---")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_resolution_{i}_{res_test['name']}_{timestamp}.mp4"
            output_path = self.test_output_dir / filename
            
            try:
                class MockTask:
                    def __init__(self):
                        self.id = f"test_resolution_task_{i}"
                
                mock_task = MockTask()
                
                success = await VideoCompositionService.compose_video(
                    task=mock_task,
                    materials=materials[:1],  # 只使用一个素材加快测试
                    background_music=background_music,
                    audio_duration=audio_analysis.total_duration,
                    cover_image_path=str(cover_file),
                    first_sentence_duration=audio_analysis.first_sentence_duration,
                    subtitle_file_path=str(subtitle_file),
                    output_path=str(output_path),
                    video_settings={
                        'resolution': res_test['resolution'],
                        'fps': 30,
                        'format': 'mp4'
                    }
                )
                
                if success and output_path.exists():
                    file_size = output_path.stat().st_size
                    logger.info(f"✅ 分辨率测试成功: {res_test['resolution']}")
                    logger.info(f"   文件大小: {file_size / (1024*1024):.2f} MB")
                    success_count += 1
                else:
                    logger.warning(f"⚠️ 分辨率测试失败: {res_test['resolution']}")
                    
            except Exception as e:
                logger.warning(f"⚠️ 分辨率测试异常: {res_test['resolution']} - {str(e)}")
        
        logger.info(f"\n=== 分辨率测试完成: {success_count}/{len(resolution_tests)} 个测试成功 ===")
        return success_count > 0
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始视频合成测试")
        logger.info(f"📁 测试输出目录: {self.test_output_dir.absolute()}")
        
        try:
            # 基本视频合成测试
            basic_success = await self.test_basic_video_composition()
            
            # 不同分辨率测试
            resolution_success = await self.test_different_resolutions()
            
            # 总结
            logger.info("\n" + "="*50)
            logger.info("📊 测试结果总结:")
            logger.info(f"   基本视频合成: {'✅ 通过' if basic_success else '❌ 失败'}")
            logger.info(f"   分辨率测试: {'✅ 通过' if resolution_success else '❌ 失败'}")
            logger.info(f"📁 输出文件位置: {self.test_output_dir.absolute()}")
            logger.info("="*50)
            
            return basic_success or resolution_success
            
        except Exception as e:
            logger.error(f"❌ 测试执行异常: {str(e)}")
            return False


async def main():
    """主函数"""
    tester = VideoCompositionTester()
    success = await tester.run_all_tests()
    
    if success:
        logger.info("🎉 视频合成测试通过！")
        sys.exit(0)
    else:
        logger.error("💥 视频合成测试失败，请检查配置和日志")
        sys.exit(1)


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(sys.stdout, format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
    
    # 运行测试
    asyncio.run(main())
