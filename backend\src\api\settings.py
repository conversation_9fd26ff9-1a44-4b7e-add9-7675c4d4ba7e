"""
Settings API routes
"""

from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
import httpx
from ..core.database import get_db
from src.core.responses import ApiResponse
from src.models.settings import Settings
from src.models.f5_tts_voices import F5TTSVoice
from src.schemas.settings import SettingsResponse, GeneralSettings, TTSConfig, LLMConfig, FeishuConfig, SettingsUpdateRequest
from typing import Dict, Any, Optional, List
from loguru import logger

router = APIRouter()

@router.get("")
async def get_settings(db: Session = Depends(get_db)):
    """获取当前设置"""
    try:
        settings = db.query(Settings).first()
        if not settings:
            # 如果没有设置记录，创建一个默认的
            settings = Settings()
            db.add(settings)
            db.commit()
            db.refresh(settings)
        
        # 转换为前端格式
        settings_data = settings.to_frontend_format()
        
        response = ApiResponse.create_success(
            data=SettingsResponse(
                tts=TTSConfig(**settings_data["tts"]),
                llm=LLMConfig(**settings_data["llm"]),
                general=GeneralSettings(**settings_data["general"]),
                feishu=FeishuConfig(**settings_data["feishu"])
            ),
            message="获取设置成功"
        )
        
        return response.model_dump()
    except Exception as e:
        logger.error(f"获取设置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取设置失败: {str(e)}")

@router.put("")
async def update_settings(
    request: SettingsUpdateRequest,
    db: Session = Depends(get_db)
):
    """更新设置"""
    try:
        settings = db.query(Settings).first()
        if not settings:
            settings = Settings()
            db.add(settings)
        
        # 更新通用设置
        if request.general:
            if request.general.theme is not None:
                setattr(settings, 'theme', request.general.theme)
            if request.general.language is not None:
                setattr(settings, 'language', request.general.language)
            if request.general.autoSave is not None:
                setattr(settings, 'auto_save', request.general.autoSave)
            if request.general.showTips is not None:
                setattr(settings, 'show_tips', request.general.showTips)
            if request.general.outputDirectory is not None:
                setattr(settings, 'output_directory', request.general.outputDirectory)
        
        # 更新TTS配置
        if request.tts:
            if request.tts.provider is not None:
                setattr(settings, 'tts_provider', request.tts.provider)
            if request.tts.voice is not None:
                setattr(settings, 'tts_voice', request.tts.voice)
            if request.tts.speed is not None:
                setattr(settings, 'tts_speed', request.tts.speed)
            if request.tts.apiKey is not None:
                setattr(settings, 'tts_api_key', request.tts.apiKey)
            if request.tts.endpoint is not None:
                setattr(settings, 'tts_endpoint', request.tts.endpoint)
            if request.tts.model is not None:
                setattr(settings, 'tts_model', request.tts.model)
            if request.tts.pitch is not None:
                setattr(settings, 'tts_pitch', request.tts.pitch)
            if request.tts.volume is not None:
                setattr(settings, 'tts_volume', request.tts.volume)
            if request.tts.f5TtsEndpoint is not None:
                setattr(settings, 'f5_tts_endpoint', request.tts.f5TtsEndpoint)

        # 更新飞书配置
        if request.feishu:
            if request.feishu.appId is not None:
                setattr(settings, 'feishu_app_id', request.feishu.appId)
            if request.feishu.appSecret is not None:
                setattr(settings, 'feishu_app_secret', request.feishu.appSecret)
            if request.feishu.appToken is not None:
                setattr(settings, 'feishu_app_token', request.feishu.appToken)
            if request.feishu.tableId is not None:
                setattr(settings, 'feishu_table_id', request.feishu.tableId)

        # 更新LLM配置
        if request.llm:
            if request.llm.provider is not None:
                setattr(settings, 'llm_provider', request.llm.provider)
            if request.llm.model is not None:
                setattr(settings, 'llm_model', request.llm.model)
            if request.llm.apiKey is not None:
                setattr(settings, 'llm_api_key', request.llm.apiKey)
            if request.llm.endpoint is not None:
                setattr(settings, 'llm_base_url', request.llm.endpoint)
            if request.llm.temperature is not None:
                setattr(settings, 'llm_temperature', request.llm.temperature)
            if request.llm.maxTokens is not None:
                setattr(settings, 'llm_max_tokens', request.llm.maxTokens)
            if request.llm.systemPrompt is not None:
                setattr(settings, 'llm_system_prompt', request.llm.systemPrompt)
        
        db.commit()
        db.refresh(settings)
        
        # 转换为前端格式
        settings_data = settings.to_frontend_format()        
        response = ApiResponse.create_success(
            data=SettingsResponse(
                tts=TTSConfig(**settings_data["tts"]),
                llm=LLMConfig(**settings_data["llm"]),
                general=GeneralSettings(**settings_data["general"]),
                feishu=FeishuConfig(**settings_data["feishu"])
            ),
            message="更新设置成功"
        )
        
        return response.model_dump()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新设置失败: {str(e)}")

@router.post("/reset")
async def reset_settings(db: Session = Depends(get_db)):
    """重置设置为默认值"""
    try:
        settings = db.query(Settings).first()
        if settings:
            db.delete(settings)
          # 创建新的默认设置
        default_settings = Settings()
        db.add(default_settings)
        db.commit()
        db.refresh(default_settings)
        
        # 转换为前端格式
        settings_data = default_settings.to_frontend_format()        
        response = ApiResponse.create_success(
            data=SettingsResponse(
                tts=TTSConfig(**settings_data["tts"]),
                llm=LLMConfig(**settings_data["llm"]),
                general=GeneralSettings(**settings_data["general"]),
                feishu=FeishuConfig(**settings_data["feishu"])
            ),
            message="重置设置成功"
        )
        
        return response.model_dump()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"重置设置失败: {str(e)}")

@router.get("/validate")
async def validate_settings(db: Session = Depends(get_db)):
    """验证当前设置的有效性"""
    try:
        settings = db.query(Settings).first()
        if not settings:
            response = ApiResponse.create_success(
                data={"valid": False, "errors": ["未找到设置记录"]},
                message="设置验证完成"
            )
            return response.model_dump()
        
        errors = []
        warnings = []
        
        # 验证TTS设置
        tts_provider = getattr(settings, 'tts_provider', None)
        llm_api_key = getattr(settings, 'llm_api_key', None)
        
        if not tts_provider:
            errors.append("TTS提供商未设置")
        elif tts_provider == "openai" and not llm_api_key:
            warnings.append("OpenAI TTS需要API密钥")
        
        # 验证LLM设置
        llm_provider = getattr(settings, 'llm_provider', None)
        
        if not llm_provider:
            errors.append("LLM提供商未设置")
        elif llm_provider in ["openai", "azure"] and not llm_api_key:
            errors.append("LLM API密钥未设置")
        
        # 验证输出设置
        output_format = getattr(settings, 'output_format', 'mp4')
        if output_format not in ["mp4", "mov", "avi"]:
            errors.append("不支持的输出格式")
        
        is_valid = len(errors) == 0
        
        response = ApiResponse.create_success(
            data={
                "valid": is_valid,
                "errors": errors,
                "warnings": warnings
            },
            message="设置验证完成"
        )        
        return response.model_dump()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"设置验证失败: {str(e)}")

@router.post("/test-llm")
async def test_llm_connection(
    api_key: str = Body(..., embed=True),
    model: str = Body("gpt-3.5-turbo", embed=True)
):
    """测试LLM（云雾API）连通性"""
    try:
        if not api_key:
            response = ApiResponse.create_error(
                message="API Key不能为空"
            )
            return response.model_dump()
        
        # 测试云雾API连接
        async with httpx.AsyncClient(timeout=30.0) as client:
            test_response = await client.post(
                "https://yunwu.ai/v1/chat/completions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {api_key}"
                },
                json={
                    "model": model,
                    "messages": [
                        {
                            "role": "user",
                            "content": "你好，这是一个连接测试。请回复确认。"
                        }
                    ],
                    "max_tokens": 50,
                    "temperature": 0.1
                }
            )

            if test_response.status_code == 200:
                data = test_response.json()
                if "choices" in data and len(data["choices"]) > 0:
                    response = ApiResponse.create_success(
                        data={
                            "connected": True,
                            "model": model,
                            "test_response": data["choices"][0]["message"]["content"]
                        },
                        message="云雾API连接测试成功"
                    )
                else:
                    response = ApiResponse.create_error(
                        message="API返回格式异常"
                    )
            else:
                error_data = test_response.json() if test_response.content else {}
                error_msg = error_data.get("error", {}).get("message", f"HTTP {test_response.status_code}")
                response = ApiResponse.create_error(
                    message=f"连接测试失败: {error_msg}"
                )
                
        return response.model_dump()
        
    except httpx.TimeoutException:
        response = ApiResponse.create_error(
            message="连接超时，请检查网络连接"
        )
        return response.model_dump()
    except Exception as e:
        response = ApiResponse.create_error(
            message=f"测试失败: {str(e)}"
        )
        return response.model_dump()

@router.post("/test-tts")
async def test_tts_connection(
    workflow_id: str = Body(..., embed=True),
    token: str = Body(..., embed=True),
    speaker_id: str = Body("zh_male_wennuanahu_moon_bigtts", embed=True),
    speed_ratio: float = Body(1.0, embed=True),
    text: str = Body("这是一个TTS连接测试。", embed=True)
):
    """测试Coze TTS连通性"""
    try:
        if not token:
            response = ApiResponse.create_error(
                message="Token不能为空"
            )
            return response.model_dump()
        
        if not workflow_id:
            response = ApiResponse.create_error(
                message="Workflow ID不能为空"
            )
            return response.model_dump()
        
        # 构建Coze工作流请求
        coze_request = {
            "speaker_id": speaker_id,
            "speed_ratio": speed_ratio,
            "text": text
        }
        
        # 测试Coze工作流API连接
        async with httpx.AsyncClient(timeout=60.0) as client:
            test_response = await client.post(
                f"https://api.coze.cn/v1/workflow/run",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}"
                },
                json={
                    "workflow_id": workflow_id,
                    "parameters": coze_request
                }
            )
            
            if test_response.status_code == 200:
                data = test_response.json()
                if data.get("code") == 0:
                    # 解析返回的音频URL
                    result_data = data.get("data", "{}")
                    if isinstance(result_data, str):
                        import json
                        try:
                            result_data = json.loads(result_data)
                        except:
                            pass
                    
                    audio_url = None
                    if isinstance(result_data, dict) and result_data.get("data", {}).get("url"):
                        audio_url = result_data["data"]["url"]
                    
                    response = ApiResponse.create_success(
                        data={
                            "connected": True,
                            "workflow_id": workflow_id,
                            "audio_url": audio_url,
                            "test_text": text
                        },
                        message="Coze TTS连接测试成功"
                    )
                else:
                    error_msg = data.get("msg", f"Coze API错误: {data.get('code')}")
                    response = ApiResponse.create_error(
                        message=f"连接测试失败: {error_msg}"
                    )
            else:
                error_data = test_response.json() if test_response.content else {}
                error_msg = error_data.get("msg", f"HTTP {test_response.status_code}")
                response = ApiResponse.create_error(
                    message=f"连接测试失败: {error_msg}"
                )
                
        return response.model_dump()
        
    except httpx.TimeoutException:
        response = ApiResponse.create_error(
            message="连接超时，请检查网络连接"
        )
        return response.model_dump()
    except Exception as e:
        response = ApiResponse.create_error(
            message=f"测试失败: {str(e)}"
        )
        return response.model_dump()


@router.post("/test-feishu")
async def test_feishu_connection(db: Session = Depends(get_db)):
    """测试飞书多维表格连通性"""
    try:
        from ..services.feishu_service import FeishuService

        feishu_service = FeishuService(db)
        result = await feishu_service.test_connection()

        if result["success"]:
            response = ApiResponse.create_success(
                data=result["data"],
                message=result["message"]
            )
        else:
            response = ApiResponse.create_error(
                message=result["message"]
            )

        return response.model_dump()

    except Exception as e:
        logger.error(f"飞书连接测试失败: {e}")
        response = ApiResponse.create_error(
            message=f"测试失败: {str(e)}"
        )
        return response.model_dump()


@router.get("/tts-voices")
async def get_tts_voices(provider: str, db: Session = Depends(get_db)):
    """获取TTS音色列表"""
    try:
        if provider == "f5-tts":
            # 获取F5-TTS音色
            voices = db.query(F5TTSVoice).filter(F5TTSVoice.is_active == True).all()
            result = [voice.to_frontend_format() for voice in voices]
        elif provider == "coze":
            # Coze TTS的内置音色
            result = [
                {
                    "id": "zh_female_zhixingnvsheng_mars_bigtts",
                    "name": "知性女声",
                    "language": "zh-CN",
                    "gender": "female"
                },
                {
                    "id": "zh_male_wennuanahu_moon_bigtts",
                    "name": "温暖男声",
                    "language": "zh-CN",
                    "gender": "male"
                }
            ]
        else:
            result = []

        response = ApiResponse.create_success(
            data=result,
            message=f"获取{provider}音色列表成功"
        )
        return response.model_dump()
    except Exception as e:
        logger.error(f"获取TTS音色失败: {e}")
        response = ApiResponse.create_error(
            message=f"获取音色失败: {str(e)}"
        )
        return response.model_dump()
