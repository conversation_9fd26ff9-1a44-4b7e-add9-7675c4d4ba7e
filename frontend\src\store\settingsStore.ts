/**
 * 设置状态管理
 * 管理TTS配置、LLM配置和通用设置
 * 数据完全存储在后端数据库中，不使用localStorage
 */

import { create } from 'zustand'
import { SettingsState, TTSConfig, LLMConfig, GeneralSettings, FeishuConfig } from '../types/store'
import DirectHttpClient from '../lib/api/directHttpClient'

// 默认配置
const defaultTTSConfig: TTSConfig = {
  provider: 'coze',
  apiKey: '',
  endpoint: 'https://api.coze.cn/v1/workflow/run', // Coze TTS 端点
  voice: 'zh_male_wennuanahu_moon_bigtts',
  model: '',
  speed: 1.2,
  pitch: 1.0,
  volume: 1.0,
  // F5-TTS特有配置
  f5TtsEndpoint: '',
}

const defaultLLMConfig: LLMConfig = {
  provider: 'yunwu',
  apiKey: '',
  endpoint: 'https://yunwu.ai/v1/chat/completions', // 云雾 API 端点
  model: 'gpt-4o',
  temperature: 0.7,
  maxTokens: 2000,
  systemPrompt: '你是一个专业的Reddit故事视频内容创作者。请创作吸引人的故事内容。',
}

const defaultGeneralSettings: GeneralSettings = {
  theme: 'system',
  language: 'zh-CN',
  autoSave: true,
  showTips: true,
  outputDirectory: './output',
}

const defaultFeishuConfig: FeishuConfig = {
  appId: '',
  appSecret: '',
  appToken: '',
  tableId: '',
}

export const useSettingsStore = create<SettingsState>((set, get) => ({
  // 初始状态
  tts: defaultTTSConfig,
  llm: defaultLLMConfig,
  general: defaultGeneralSettings,
  feishu: defaultFeishuConfig,

  // TTS配置操作
  updateTTSConfig: (config: Partial<TTSConfig>) => {
    set((state) => ({
      tts: { ...state.tts, ...config }
    }))
  },

  // LLM配置操作
  updateLLMConfig: (config: Partial<LLMConfig>) => {
    set((state) => ({
      llm: { ...state.llm, ...config }
    }))
  },

  // 通用设置操作
  updateGeneralSettings: (settings: Partial<GeneralSettings>) => {
    set((state) => ({
      general: { ...state.general, ...settings }
    }))
  },

  // 飞书配置操作
  updateFeishuConfig: (config: Partial<FeishuConfig>) => {
    set((state) => ({
      feishu: { ...state.feishu, ...config }
    }))
  },

  // 重置为默认值
  resetToDefault: () => {
    set({
      tts: defaultTTSConfig,
      llm: defaultLLMConfig,
      general: defaultGeneralSettings,
      feishu: defaultFeishuConfig,
    })
  },

  // 从后端加载设置
  loadFromBackend: async () => {
    try {
      const settingsClient = new DirectHttpClient('/api/settings')
      const data = await settingsClient.get<any>('')
      
      if (data.success) {
        // 直接使用后端数据，不合并默认配置
        set({
          tts: data.data.tts,
          llm: data.data.llm,
          general: data.data.general,
          feishu: data.data.feishu || defaultFeishuConfig,
        })
        console.log('Settings loaded from backend:', data.data)
      }
    } catch (error) {
      console.error('Failed to load settings from backend:', error)
    }
  },

  // 保存到后端
  saveToBackend: async () => {
    try {
      const { tts, llm, general, feishu } = get()
      const settingsClient = new DirectHttpClient('/api/settings')
      const data = await settingsClient.put<any>('', {
        tts,
        llm,
        general,
        feishu
      })

      if (data.success) {
        if (data.success) {
          console.log('Settings saved to backend')
          return true
        }
      }
      return false
    } catch (error) {
      console.error('Failed to save settings to backend:', error)
      return false
    }
  },

  // 兼容旧接口
  loadFromStorage: async () => await get().loadFromBackend(),
  saveToStorage: async () => await get().saveToBackend(),
}))

export type { SettingsState, TTSConfig, LLMConfig, GeneralSettings }
