"""
封面生成测试脚本

此脚本用于测试封面图片生成功能。
使用网页截图服务生成视频封面，支持不同的模板和样式。

使用方法：
1. 确保数据库中有封面模板和账号数据
2. 运行脚本：python -m backend.src.video_generation_test.test_cover_generation
3. 检查生成的封面图片

注意：
- 需要安装Playwright：pip install playwright
- 需要安装浏览器：playwright install chromium
- 确保数据库中有可用的封面模板和账号
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.src.services.cover_screenshot_service import cover_screenshot_service
from backend.src.database import get_session_maker
from backend.src.models import Account, CoverTemplate


class CoverGenerationTester:
    """封面生成测试器"""
    
    def __init__(self):
        self.session_maker = get_session_maker()
        self.test_output_dir = Path("backend/src/video_generation_test/output/covers")
        self.test_output_dir.mkdir(parents=True, exist_ok=True)
    
    def get_test_data(self):
        """获取测试所需的数据"""
        db = self.session_maker()
        try:
            # 获取第一个可用的账号
            account = db.query(Account).first()
            if not account:
                logger.error("❌ 数据库中没有找到账号数据")
                return None, None
            
            # 获取第一个可用的封面模板
            template = db.query(CoverTemplate).first()
            if not template:
                logger.error("❌ 数据库中没有找到封面模板")
                return account, None
            
            logger.info(f"✅ 找到测试账号: {account.name}")
            logger.info(f"✅ 找到封面模板: {template.name}")
            
            return account, template
            
        finally:
            db.close()
    
    async def test_basic_cover_generation(self):
        """测试基本封面生成功能"""
        logger.info("=== 开始测试基本封面生成功能 ===")
        
        # 获取测试数据
        account, template = self.get_test_data()
        if not account or not template:
            logger.error("❌ 缺少测试数据，无法进行封面生成测试")
            return False
        
        # 测试用例
        test_cases = [
            {
                "name": "短标题测试",
                "title": "今天天气很好"
            },
            {
                "name": "长标题测试",
                "title": "这是一个比较长的标题，用来测试封面生成时的文字排版和换行效果"
            },
            {
                "name": "包含标点的标题",
                "title": "你知道吗？这个秘密让所有人都震惊了！"
            },
            {
                "name": "英文标题测试",
                "title": "Amazing Discovery That Will Change Everything"
            },
            {
                "name": "中英混合标题",
                "title": "AI技术的Amazing突破，你准备好了吗？"
            }
        ]
        
        success_count = 0
        db = self.session_maker()
        
        try:
            for i, test_case in enumerate(test_cases, 1):
                logger.info(f"\n--- 测试用例 {i}: {test_case['name']} ---")
                
                # 生成输出文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"test_cover_{i}_{test_case['name']}_{timestamp}.png"
                output_path = self.test_output_dir / filename
                
                try:
                    # 创建模拟任务对象
                    class MockTask:
                        def __init__(self, title):
                            self.id = f"test_task_{i}"
                            self.first_sentence = title
                            self.account_id = account.id
                    
                    mock_task = MockTask(test_case['title'])
                    
                    # 调用封面生成服务
                    success = await cover_screenshot_service.generate_cover_for_video_task(
                        task=mock_task,
                        template_id=template.id,
                        account=account,
                        title=test_case['title'],
                        output_path=str(output_path),
                        db=db
                    )
                    
                    if success and output_path.exists():
                        file_size = output_path.stat().st_size
                        logger.info(f"✅ 封面生成成功: {filename}")
                        logger.info(f"   文件大小: {file_size} bytes")
                        logger.info(f"   标题: {test_case['title']}")
                        success_count += 1
                    else:
                        logger.error(f"❌ 封面生成失败: {test_case['name']}")
                        
                except Exception as e:
                    logger.error(f"❌ 测试用例执行异常: {test_case['name']} - {str(e)}")
        
        finally:
            db.close()
        
        logger.info(f"\n=== 基本封面生成测试完成: {success_count}/{len(test_cases)} 个用例成功 ===")
        return success_count == len(test_cases)
    
    async def test_fallback_cover_generation(self):
        """测试备用封面生成功能"""
        logger.info("=== 开始测试备用封面生成功能 ===")
        
        # 获取测试数据
        account, template = self.get_test_data()
        if not account or not template:
            logger.error("❌ 缺少测试数据，无法进行备用封面测试")
            return False
        
        # 测试备用封面生成（使用PIL生成简单封面）
        test_title = "备用封面生成测试"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_fallback_cover_{timestamp}.png"
        output_path = self.test_output_dir / filename
        
        try:
            # 导入封面生成辅助类
            from backend.src.services.video_generation_helpers import VideoGenerationServiceHelpers
            
            helper = VideoGenerationServiceHelpers(self.session_maker)
            
            # 调用简单封面生成方法
            success = await helper._generate_simple_cover(
                template=template,
                account=account,
                title=test_title,
                output_path=str(output_path)
            )
            
            if success and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"✅ 备用封面生成成功: {filename}")
                logger.info(f"   文件大小: {file_size} bytes")
                return True
            else:
                logger.error("❌ 备用封面生成失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 备用封面生成异常: {str(e)}")
            return False
    
    async def test_different_templates(self):
        """测试不同模板的封面生成"""
        logger.info("=== 开始测试不同模板封面生成 ===")
        
        # 获取测试数据
        account, _ = self.get_test_data()
        if not account:
            logger.error("❌ 缺少账号数据，无法进行模板测试")
            return False
        
        # 获取所有可用模板
        db = self.session_maker()
        try:
            templates = db.query(CoverTemplate).limit(3).all()  # 限制测试3个模板
            if not templates:
                logger.error("❌ 数据库中没有找到封面模板")
                return False
            
            logger.info(f"📋 找到 {len(templates)} 个模板进行测试")
            
            success_count = 0
            test_title = "模板测试标题"
            
            for i, template in enumerate(templates, 1):
                logger.info(f"\n--- 测试模板 {i}: {template.name} ---")
                
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"test_template_{i}_{template.name}_{timestamp}.png"
                output_path = self.test_output_dir / filename
                
                try:
                    # 创建模拟任务对象
                    class MockTask:
                        def __init__(self):
                            self.id = f"test_template_task_{i}"
                            self.first_sentence = test_title
                            self.account_id = account.id
                    
                    mock_task = MockTask()
                    
                    # 调用封面生成服务
                    success = await cover_screenshot_service.generate_cover_for_video_task(
                        task=mock_task,
                        template_id=template.id,
                        account=account,
                        title=test_title,
                        output_path=str(output_path),
                        db=db
                    )
                    
                    if success and output_path.exists():
                        file_size = output_path.stat().st_size
                        logger.info(f"✅ 模板封面生成成功: {template.name}")
                        logger.info(f"   文件大小: {file_size} bytes")
                        success_count += 1
                    else:
                        logger.warning(f"⚠️ 模板封面生成失败: {template.name}")
                        
                except Exception as e:
                    logger.warning(f"⚠️ 模板测试异常: {template.name} - {str(e)}")
            
            logger.info(f"\n=== 模板测试完成: {success_count}/{len(templates)} 个模板成功 ===")
            return success_count > 0
            
        finally:
            db.close()
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始封面生成测试")
        logger.info(f"📁 测试输出目录: {self.test_output_dir.absolute()}")
        
        try:
            # 基本封面生成测试
            basic_success = await self.test_basic_cover_generation()
            
            # 备用封面生成测试
            fallback_success = await self.test_fallback_cover_generation()
            
            # 不同模板测试
            template_success = await self.test_different_templates()
            
            # 总结
            logger.info("\n" + "="*50)
            logger.info("📊 测试结果总结:")
            logger.info(f"   基本封面生成: {'✅ 通过' if basic_success else '❌ 失败'}")
            logger.info(f"   备用封面生成: {'✅ 通过' if fallback_success else '❌ 失败'}")
            logger.info(f"   模板测试: {'✅ 通过' if template_success else '❌ 失败'}")
            logger.info(f"📁 输出文件位置: {self.test_output_dir.absolute()}")
            logger.info("="*50)
            
            return basic_success or fallback_success  # 至少一种方式成功即可
            
        except Exception as e:
            logger.error(f"❌ 测试执行异常: {str(e)}")
            return False


async def main():
    """主函数"""
    tester = CoverGenerationTester()
    success = await tester.run_all_tests()
    
    if success:
        logger.info("🎉 封面生成测试通过！")
        sys.exit(0)
    else:
        logger.error("💥 封面生成测试失败，请检查配置和日志")
        sys.exit(1)


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(sys.stdout, format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
    
    # 运行测试
    asyncio.run(main())
