# 视频生成测试套件

这个测试套件用于测试视频生成服务的各个环节，包括语音生成、字幕生成、封面生成和视频合成。

## 📁 目录结构

```
backend/src/video_generation_test/
├── __init__.py                    # 模块初始化文件
├── README.md                      # 使用说明文档
├── run_all_tests.py              # 综合测试脚本
├── test_audio_generation.py      # 语音生成测试
├── test_subtitle_generation.py   # 字幕生成测试
├── test_cover_generation.py      # 封面生成测试
├── test_video_composition.py     # 视频合成测试
└── output/                       # 测试输出目录
    ├── audio/                    # 生成的音频文件
    ├── subtitles/                # 生成的字幕文件
    ├── covers/                   # 生成的封面图片
    └── videos/                   # 生成的视频文件
```

## 🚀 快速开始

### 1. 环境准备

确保已安装所需依赖：

```bash
# 基础依赖
pip install loguru sqlalchemy pillow aiohttp

# TTS相关
pip install httpx

# 音频分析相关
pip install openai-whisper

# 视频处理相关
pip install ffmpeg-python

# 封面生成相关（可选）
pip install playwright
playwright install chromium
```

### 2. 系统配置

在运行测试前，请确保：

1. **数据库配置**：系统已连接到数据库
2. **TTS配置**：在系统设置中配置了TTS服务（Coze或F5-TTS）
3. **素材数据**：数据库中有视频素材、背景音乐、封面模板、账号等数据
4. **FFmpeg**：系统已安装FFmpeg并添加到PATH

### 3. 运行测试

#### 运行所有测试（推荐）

```bash
# 在项目根目录下运行
python -m backend.src.video_generation_test.run_all_tests
```

#### 运行单个测试

```bash
# 语音生成测试
python -m backend.src.video_generation_test.run_all_tests --test audio

# 字幕生成测试
python -m backend.src.video_generation_test.run_all_tests --test subtitle

# 封面生成测试
python -m backend.src.video_generation_test.run_all_tests --test cover

# 视频合成测试
python -m backend.src.video_generation_test.run_all_tests --test video
```

#### 详细日志模式

```bash
python -m backend.src.video_generation_test.run_all_tests --verbose
```

## 📋 测试环节详解

### 1. 语音生成测试 (`test_audio_generation.py`)

**功能**：测试TTS服务的语音生成功能

**测试内容**：
- 基本语音生成（不同语速）
- 长文本处理
- 不同音色测试
- TTS配置验证

**样例参数**：
```python
# 测试文本
test_text = "这是一个测试语音生成的文本。今天天气很好，适合出去走走。"

# 测试参数
voice = "zh-CN-XiaoxiaoNeural"  # 音色
speed = 1.0                     # 语速
```

**输出**：`output/audio/` 目录下的MP3文件

### 2. 字幕生成测试 (`test_subtitle_generation.py`)

**功能**：测试通过语音文件生成字幕的功能

**测试内容**：
- Whisper音频分析
- 不同字幕配置（每屏词数）
- SRT格式验证
- 时间戳准确性

**样例参数**：
```python
# 字幕配置
subtitle_config = {
    'words_per_screen': 1,      # 每屏显示词数
    'include_all_words': True   # 是否包含所有词语
}
```

**输出**：`output/subtitles/` 目录下的SRT文件

### 3. 封面生成测试 (`test_cover_generation.py`)

**功能**：测试封面图片生成功能

**测试内容**：
- 网页截图封面生成
- 不同标题长度处理
- 多模板测试
- 备用封面生成

**样例参数**：
```python
# 测试标题
titles = [
    "今天天气很好",
    "这是一个比较长的标题，用来测试封面生成时的文字排版和换行效果",
    "你知道吗？这个秘密让所有人都震惊了！"
]
```

**输出**：`output/covers/` 目录下的PNG文件

### 4. 视频合成测试 (`test_video_composition.py`)

**功能**：测试视频合成功能

**测试内容**：
- 基本视频合成
- 不同分辨率测试
- 音频混合（语音+背景音乐）
- 字幕叠加
- 封面动画效果

**样例参数**：
```python
# 视频设置
video_settings = {
    'resolution': '1080x1920',  # 分辨率
    'fps': 30,                  # 帧率
    'format': 'mp4'             # 格式
}

# 音频设置
audio_settings = {
    'speech_volume': 1.0,              # 语音音量
    'background_music_volume': 0.15,   # 背景音乐音量
    'enable_background_music': True    # 是否启用背景音乐
}
```

**输出**：`output/videos/` 目录下的MP4文件

## 🔧 故障排除

### 常见问题

1. **TTS配置错误**
   ```
   ❌ TTS配置未找到，请先在系统设置中配置TTS服务
   ```
   **解决方案**：在系统设置中配置Coze或F5-TTS服务

2. **Whisper模型下载失败**
   ```
   ❌ Whisper库未安装。请运行 'pip install openai-whisper'
   ```
   **解决方案**：安装Whisper并确保网络连接正常

3. **FFmpeg未找到**
   ```
   ❌ FFmpeg not found
   ```
   **解决方案**：安装FFmpeg并添加到系统PATH

4. **数据库数据缺失**
   ```
   ❌ 数据库中没有找到视频素材
   ```
   **解决方案**：确保数据库中有视频素材、背景音乐等数据

### 日志分析

测试脚本会输出详细的日志信息：

- ✅ 表示成功
- ❌ 表示失败
- ⚠️ 表示警告
- 📁 表示文件路径
- 📊 表示统计信息

## 📊 测试结果解读

### 成功示例
```
📊 视频生成全流程测试结果总结
============================================================
   语音生成: ✅ 通过
   字幕生成: ✅ 通过
   封面生成: ✅ 通过
   视频合成: ✅ 通过

总体结果: 4/4 个测试通过
🎉 所有测试通过！视频生成功能正常
```

### 部分失败示例
```
📊 视频生成全流程测试结果总结
============================================================
   语音生成: ✅ 通过
   字幕生成: ✅ 通过
   封面生成: ❌ 失败
   视频合成: ✅ 通过

总体结果: 3/4 个测试通过
⚠️ 部分测试通过，请检查失败的测试项
```

## 🛠️ 自定义测试

### 添加新的测试用例

1. 在对应的测试文件中添加新的测试方法
2. 在测试用例列表中添加新的配置
3. 运行测试验证功能

### 修改测试参数

可以直接修改测试脚本中的参数：

```python
# 修改语音测试参数
test_cases = [
    {
        "name": "自定义测试",
        "voice": "your-voice-id",
        "speed": 1.2,
        "text": "你的测试文本"
    }
]
```

## 📞 技术支持

如果遇到问题：

1. 检查系统配置是否正确
2. 查看详细日志信息
3. 确认依赖库是否正确安装
4. 验证数据库数据是否完整

测试脚本设计为尽可能提供详细的错误信息和解决建议。
