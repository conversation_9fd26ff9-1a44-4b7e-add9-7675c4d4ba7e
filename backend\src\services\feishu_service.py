"""
飞书多维表格API服务
"""

import httpx
import json
import time
from typing import List, Dict, Any, Optional, <PERSON><PERSON>
from loguru import logger
from sqlalchemy.orm import Session
from ..models.settings import Settings


class FeishuService:
    """飞书多维表格服务"""
    
    def __init__(self, db_session: Session):
        self.db_session = db_session
        self.base_url = "https://open.feishu.cn"
        self._access_token = None
        self._token_expires_at = 0
        
    def _get_feishu_config(self) -> Tuple[str, str, str, str]:
        """获取飞书配置"""
        settings = self.db_session.query(Settings).first()
        if not settings:
            raise ValueError("未找到系统设置")
            
        app_id = getattr(settings, 'feishu_app_id', None)
        app_secret = getattr(settings, 'feishu_app_secret', None)
        app_token = getattr(settings, 'feishu_app_token', None)
        table_id = getattr(settings, 'feishu_table_id', None)
        
        if not all([app_id, app_secret, app_token, table_id]):
            raise ValueError("飞书配置不完整，请检查app_id、app_secret、app_token、table_id")
            
        return app_id, app_secret, app_token, table_id
    
    async def _get_access_token(self, max_retries: int = 3) -> str:
        """获取访问令牌，支持重试机制"""
        # 检查token是否还有效（提前5分钟刷新）
        if self._access_token and time.time() < (self._token_expires_at - 300):
            return self._access_token
            
        app_id, app_secret, _, _ = self._get_feishu_config()
        
        for attempt in range(max_retries):
            try:
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.post(
                        f"{self.base_url}/open-apis/auth/v3/tenant_access_token/internal",
                        headers={"Content-Type": "application/json"},
                        json={
                            "app_id": app_id,
                            "app_secret": app_secret
                        }
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        if data.get("code") == 0:
                            self._access_token = data["tenant_access_token"]
                            # 设置过期时间（通常是2小时，这里设置为1.5小时后过期）
                            self._token_expires_at = time.time() + 5400
                            logger.info("飞书访问令牌获取成功")
                            return self._access_token
                        else:
                            error_msg = data.get("msg", "获取访问令牌失败")
                            logger.error(f"飞书API错误: {error_msg}")
                            if attempt == max_retries - 1:
                                raise Exception(f"获取飞书访问令牌失败: {error_msg}")
                    else:
                        logger.error(f"HTTP错误: {response.status_code}")
                        if attempt == max_retries - 1:
                            raise Exception(f"HTTP请求失败: {response.status_code}")
                            
            except httpx.TimeoutException:
                logger.warning(f"飞书API请求超时，第{attempt + 1}次重试")
                if attempt == max_retries - 1:
                    raise Exception("飞书API请求超时")
            except Exception as e:
                logger.error(f"获取访问令牌失败，第{attempt + 1}次重试: {str(e)}")
                if attempt == max_retries - 1:
                    raise
                    
            # 重试前等待
            if attempt < max_retries - 1:
                await asyncio.sleep(1)
                
        raise Exception("获取访问令牌失败，已达到最大重试次数")
    
    async def search_records(self, count: int, status_value: str = "待生成", max_retries: int = 3) -> List[Dict[str, Any]]:
        """
        查询记录，获取指定数量的待生成记录
        
        Args:
            count: 需要获取的记录数量
            status_value: 状态字段的值，默认为"待生成"
            max_retries: 最大重试次数
            
        Returns:
            记录列表，每个记录包含record_id和字段数据
        """
        _, _, app_token, table_id = self._get_feishu_config()
        access_token = await self._get_access_token(max_retries)
        
        all_records = []
        page_token = None
        
        # 分页获取记录，直到获取足够数量或没有更多记录
        while len(all_records) < count:
            for attempt in range(max_retries):
                try:
                    # 构建请求参数
                    params = {
                        "page_size": min(500, count - len(all_records))  # 每页最多500条
                    }
                    if page_token:
                        params["page_token"] = page_token
                    
                    async with httpx.AsyncClient(timeout=60.0) as client:
                        response = await client.post(
                            f"{self.base_url}/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records/search",
                            headers={
                                "Authorization": f"Bearer {access_token}",
                                "Content-Type": "application/json"
                            },
                            json={
                                "filter": {
                                    "conditions": [
                                        {
                                            "field_name": "状态",
                                            "operator": "is",
                                            "value": [status_value]
                                        }
                                    ],
                                    "conjunction": "and"
                                }
                            },
                            params=params
                        )
                        
                        if response.status_code == 200:
                            data = response.json()
                            if data.get("code") == 0:
                                records = data.get("data", {}).get("items", [])
                                all_records.extend(records)
                                
                                # 检查是否还有更多页面
                                page_token = data.get("data", {}).get("page_token")
                                if not page_token:
                                    break  # 没有更多页面了
                                    
                                logger.info(f"已获取 {len(all_records)} 条记录")
                                break  # 成功，跳出重试循环
                            else:
                                error_msg = data.get("msg", "查询记录失败")
                                logger.error(f"飞书API错误: {error_msg}")
                                if attempt == max_retries - 1:
                                    raise Exception(f"查询飞书记录失败: {error_msg}")
                        else:
                            logger.error(f"HTTP错误: {response.status_code}")
                            if attempt == max_retries - 1:
                                raise Exception(f"HTTP请求失败: {response.status_code}")
                                
                except httpx.TimeoutException:
                    logger.warning(f"飞书API请求超时，第{attempt + 1}次重试")
                    if attempt == max_retries - 1:
                        raise Exception("飞书API请求超时")
                except Exception as e:
                    logger.error(f"查询记录失败，第{attempt + 1}次重试: {str(e)}")
                    if attempt == max_retries - 1:
                        raise
                        
                # 重试前等待
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
            
            # 如果没有更多页面，退出循环
            if not page_token:
                break
        
        # 返回所需数量的记录
        result_records = all_records[:count]
        logger.info(f"成功获取 {len(result_records)} 条待生成记录")
        return result_records
    
    async def update_records(self, updates: List[Dict[str, Any]], max_retries: int = 3) -> bool:
        """
        批量更新记录
        
        Args:
            updates: 更新数据列表，每个元素包含record_id和fields
            max_retries: 最大重试次数
            
        Returns:
            是否更新成功
        """
        if not updates:
            return True
            
        _, _, app_token, table_id = self._get_feishu_config()
        access_token = await self._get_access_token(max_retries)
        
        # 飞书API限制每次最多更新500条记录
        batch_size = 500
        
        for i in range(0, len(updates), batch_size):
            batch_updates = updates[i:i + batch_size]
            
            for attempt in range(max_retries):
                try:
                    async with httpx.AsyncClient(timeout=60.0) as client:
                        response = await client.post(
                            f"{self.base_url}/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records/batch_update",
                            headers={
                                "Authorization": f"Bearer {access_token}",
                                "Content-Type": "application/json"
                            },
                            json={"records": batch_updates}
                        )
                        
                        if response.status_code == 200:
                            data = response.json()
                            if data.get("code") == 0:
                                logger.info(f"成功更新 {len(batch_updates)} 条记录")
                                break  # 成功，跳出重试循环
                            else:
                                error_msg = data.get("msg", "更新记录失败")
                                logger.error(f"飞书API错误: {error_msg}")
                                if attempt == max_retries - 1:
                                    raise Exception(f"更新飞书记录失败: {error_msg}")
                        else:
                            logger.error(f"HTTP错误: {response.status_code}")
                            if attempt == max_retries - 1:
                                raise Exception(f"HTTP请求失败: {response.status_code}")
                                
                except httpx.TimeoutException:
                    logger.warning(f"飞书API请求超时，第{attempt + 1}次重试")
                    if attempt == max_retries - 1:
                        raise Exception("飞书API请求超时")
                except Exception as e:
                    logger.error(f"更新记录失败，第{attempt + 1}次重试: {str(e)}")
                    if attempt == max_retries - 1:
                        raise
                        
                # 重试前等待
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
        
        return True
    
    async def test_connection(self, max_retries: int = 3) -> Dict[str, Any]:
        """
        测试飞书连接
        
        Returns:
            测试结果字典
        """
        try:
            # 测试获取访问令牌
            access_token = await self._get_access_token(max_retries)
            
            # 测试查询记录（只查询1条）
            records = await self.search_records(1, "待生成", max_retries)
            
            return {
                "success": True,
                "message": "飞书连接测试成功",
                "data": {
                    "access_token_obtained": bool(access_token),
                    "records_found": len(records)
                }
            }
        except Exception as e:
            logger.error(f"飞书连接测试失败: {str(e)}")
            return {
                "success": False,
                "message": f"飞书连接测试失败: {str(e)}"
            }


# 需要导入asyncio
import asyncio
