# 飞书多维表格集成使用说明

## 功能概述

本次升级为Reddit故事视频生成器添加了飞书多维表格支持，实现了：

1. **飞书多维表格配置** - 在设置页面配置飞书应用参数
2. **文案导入** - 从飞书多维表格导入待生成的文案
3. **状态回写** - 视频生成完成后自动更新飞书表格状态

## 配置步骤

### 1. 飞书应用配置

1. 访问 [飞书开放平台](https://open.feishu.cn/)
2. 创建企业自建应用
3. 获取以下信息：
   - **App ID**: 应用的唯一标识符
   - **App Secret**: 应用密钥
   - **App Token**: 多维表格的应用令牌
   - **Table ID**: 目标多维表格的ID

### 2. 多维表格字段设置

确保您的飞书多维表格包含以下字段：

| 字段名称 | 字段类型 | 说明 |
|---------|---------|------|
| 英文文案 | 多行文本 | 视频文案内容 |
| 英文标题 | 单行文本 | 视频标题（可选） |
| 朗读性别 | 单选 | male/female |
| 状态 | 单选 | 待生成/待发布/生成失败 |
| 视频文件名 | 单行文本 | 生成的视频文件路径 |
| 生成时间 | 日期时间 | 视频生成完成时间 |
| 生成错误信息 | 多行文本 | 错误信息（如有） |

### 3. 系统配置

1. 启动应用并访问设置页面
2. 在"飞书多维表格"配置区域填入：
   - App ID
   - App Secret  
   - App Token
   - Table ID
3. 点击"测试连接"验证配置
4. 保存设置

## 使用方法

### 批量视频生成

1. 访问"批量生成"页面
2. 在"文案导入"区域选择"飞书多维表格"
3. 设置导入条数（1-1000）
4. 点击"开始导入"
5. 系统将自动导入状态为"待生成"的记录
6. 配置其他生成参数
7. 提交作业开始生成

### 状态回写机制

视频生成过程中，系统会自动：

- **生成成功时**：
  - 状态更新为"待发布"
  - 记录视频文件绝对路径
  - 记录生成完成时间
  - 清空错误信息

- **生成失败时**：
  - 状态更新为"生成失败"
  - 记录详细错误信息
  - 记录失败时间

## 技术实现

### 后端改动

1. **数据模型扩展**
   - `Settings` 模型添加飞书配置字段
   - `VideoGenerationTask` 模型添加 `feishu_record_id` 和 `data_source` 字段

2. **飞书服务集成**
   - `FeishuService` 类实现飞书API调用
   - 支持获取access_token、查询记录、批量更新
   - 内置重试机制和错误处理

3. **API扩展**
   - `/api/video-generator/import-feishu` - 飞书导入接口
   - `/api/settings/test-feishu` - 连接测试接口
   - 扩展批量作业创建接口支持飞书数据源

4. **视频生成流程改造**
   - 任务完成时自动调用飞书状态回写
   - 支持成功和失败状态的不同处理

### 前端改动

1. **设置页面**
   - 添加飞书配置区域
   - 实现连接测试功能
   - 集成到全局设置存储

2. **批量生成页面**
   - 添加数据源选择（Excel/飞书）
   - 飞书导入界面和逻辑
   - 支持飞书记录ID传递

3. **类型定义**
   - 扩展相关接口支持飞书字段
   - 新增 `FeishuConfig` 和 `FeishuImportResponse` 类型

## 注意事项

1. **权限要求**
   - 飞书应用需要多维表格读写权限
   - 确保应用已添加到对应的多维表格

2. **字段映射**
   - 字段名称必须完全匹配
   - 状态值区分大小写

3. **性能考虑**
   - 大批量导入时会分页处理
   - 状态回写采用异步处理，不影响主流程

4. **错误处理**
   - 飞书API调用失败不会中断视频生成
   - 所有错误都会记录到日志中

## 故障排除

### 连接测试失败
- 检查网络连接
- 验证App ID和App Secret
- 确认应用权限设置

### 导入数据为空
- 检查表格中是否有"待生成"状态的记录
- 验证字段名称是否正确
- 确认App Token和Table ID

### 状态回写失败
- 查看后端日志获取详细错误信息
- 检查飞书应用权限
- 验证记录ID是否有效

## 完成状态

✅ 飞书多维表格支持 - 后端实现
✅ 飞书多维表格支持 - 前端设置页面  
✅ 飞书多维表格支持 - 批量生成页面升级
✅ 飞书多维表格支持 - 视频生成流程改造

所有功能已完成并可正常使用！
