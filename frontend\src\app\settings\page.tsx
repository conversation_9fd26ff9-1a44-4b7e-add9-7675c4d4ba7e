/**
 * 设置页面 - 按照原型设计实现
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useSettingsStore } from '../../store/settingsStore'
import { useNotificationStore } from '../../store/notificationStore'
import { ttsVoices } from '../../config/ttsConfig'
import DirectHttpClient from '../../lib/api/directHttpClient'
import F5TTSVoiceManager from '../../components/F5TTSVoiceManager'
import { settingsApi } from '../../services/apiService'

type TestResult = 'success' | 'error' | null



export default function SettingsPage() {
  const [isTestingTTS, setIsTestingTTS] = useState(false)
  const [isTestingLLM, setIsTestingLLM] = useState(false)
  const [isTestingFeishu, setIsTestingFeishu] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [testResults, setTestResults] = useState<{
    tts: TestResult
    llm: TestResult
    feishu: TestResult
  }>({ tts: null, llm: null, feishu: null })


  
  const {
    tts,
    llm,
    feishu,
    updateTTSConfig,
    updateLLMConfig,
    updateFeishuConfig,
    resetToDefault,
    loadFromBackend,
    saveToBackend
  } = useSettingsStore()

  const { addNotification } = useNotificationStore()

  // 组件加载时从后端获取设置
  useEffect(() => {
    loadFromBackend()
  }, [])

  const handleTTSProviderChange = (provider: string) => {
    updateTTSConfig({ provider: provider as any })
  }

  const handleLLMProviderChange = (provider: string) => {
    updateLLMConfig({ provider: provider as any })
  }

  const handleTestTTS = async () => {
    setIsTestingTTS(true)
    setTestResults(prev => ({ ...prev, tts: null }))

    try {
      const settingsClient = new DirectHttpClient('/api/settings')

      if (tts.provider === 'coze') {
        // 验证Coze TTS必填字段
        if (!tts.apiKey) {
          throw new Error('请输入Token')
        }
        if (!tts.model) {
          throw new Error('请输入Workflow ID')
        }

        // 调用Coze TTS测试API
        const data = await settingsClient.post<any>('/test-tts', {
          workflow_id: tts.model,
          token: tts.apiKey,
          speaker_id: tts.voice || 'zh_male_wennuanahu_moon_bigtts',
          speed_ratio: tts.speed || 1.2,
          text: "这是一个TTS连接测试，如果您听到这段话，说明Coze TTS服务配置正确。"
        })

        if (data.success) {
          setTestResults(prev => ({ ...prev, tts: 'success' }))
          addNotification({
            type: 'success',
            title: 'TTS测试成功',
            message: 'Coze TTS服务连接正常',
            duration: 3000
          })
        } else {
          throw new Error(data.message || 'Coze TTS测试失败')
        }
      } else if (tts.provider === 'f5-tts') {
        // 验证F5-TTS必填字段
        if (!tts.f5TtsEndpoint) {
          throw new Error('请输入F5-TTS服务端点')
        }

        // 测试F5-TTS服务连接
        try {
          const response = await fetch(`${tts.f5TtsEndpoint}`, {
            method: 'GET'
          })

          if (response.ok) {
            setTestResults(prev => ({ ...prev, tts: 'success' }))
            addNotification({
              type: 'success',
              title: 'F5-TTS测试成功',
              message: 'F5-TTS服务连接正常',
              duration: 3000
            })
          } else {
            throw new Error(`F5-TTS服务响应错误: ${response.status}`)
          }
        } catch (fetchError) {
          throw new Error(`无法连接到F5-TTS服务: ${fetchError instanceof Error ? fetchError.message : '连接失败'}`)
        }
      } else {
        throw new Error('请选择TTS提供商')
      }
    } catch (error) {
      console.error('TTS连接测试失败:', error)
      setTestResults(prev => ({ ...prev, tts: 'error' }))
      addNotification({
        type: 'error',
        title: 'TTS测试失败',
        message: error instanceof Error ? error.message : 'TTS连接测试失败',
        duration: 5000
      })
    }
    setIsTestingTTS(false)
  }

  const handleTestLLM = async () => {
    setIsTestingLLM(true)
    setTestResults(prev => ({ ...prev, llm: null }))
    
    try {
      // 验证API Key
      if (!llm.apiKey) {
        throw new Error('请输入API Key')
      }

      // 调用后端API测试连通性
      const settingsClient = new DirectHttpClient('/api/settings')
      const data = await settingsClient.post<any>('/test-llm', {
        api_key: llm.apiKey,
        model: llm.model || 'gpt-4o'
      })
      
      if (data.success) {
        setTestResults(prev => ({ ...prev, llm: 'success' }))
        addNotification({
          type: 'success',
          title: 'LLM测试成功',
          message: '云雾API服务连接正常',
          duration: 3000
        })
      } else {
        throw new Error(data.message || 'API测试失败')
      }
    } catch (error) {
      console.error('LLM连接测试失败:', error)
      setTestResults(prev => ({ ...prev, llm: 'error' }))
      addNotification({
        type: 'error',
        title: 'LLM测试失败',
        message: error instanceof Error ? error.message : 'LLM连接测试失败',
        duration: 5000
      })
    }
    setIsTestingLLM(false)
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // 直接调用store的saveToBackend方法
      const success = await saveToBackend()
      
      if (success) {
        console.log('设置保存成功')
        addNotification({
          type: 'success',
          title: '保存成功',
          message: '设置已成功保存到数据库',
          duration: 3000
        })
      } else {
        throw new Error('保存设置失败')
      }
    } catch (error) {
      console.error('保存失败:', error)
      addNotification({
        type: 'error',
        title: '保存失败',
        message: error instanceof Error ? error.message : '保存设置时发生未知错误',
        duration: 5000
      })
    }
    setIsSaving(false)
  }

  const handleTestFeishu = async () => {
    setIsTestingFeishu(true)
    setTestResults(prev => ({ ...prev, feishu: null }))

    try {
      // 验证必填字段
      if (!feishu.appId || !feishu.appSecret || !feishu.appToken || !feishu.tableId) {
        throw new Error('请填写所有必填字段')
      }

      // 先保存配置，然后测试连接
      await saveToBackend()

      // 测试连接
      const response = await settingsApi.testFeishuConnection()
      console.log('飞书测试 ==> ', response)
      if (response.data && response.data.access_token_obtained) {
        setTestResults(prev => ({ ...prev, feishu: 'success' }))
        addNotification({
          type: 'success',
          title: '飞书连接测试成功',
          message: '飞书多维表格连接正常',
          duration: 3000
        })
      } else {
        throw new Error(response.message || '飞书连接测试失败')
      }
    } catch (error: any) {
      setTestResults(prev => ({ ...prev, feishu: 'error' }))
      addNotification({
        type: 'error',
        title: '飞书连接测试失败',
        message: error.message || '连接测试失败，请检查配置',
        duration: 5000
      })
    } finally {
      setIsTestingFeishu(false)
    }
  }

  const handleReset = () => {
    if (confirm('确定要重置所有设置到默认值吗？')) {
      resetToDefault()
      setTestResults({ tts: null, llm: null, feishu: null })

      addNotification({
        type: 'info',
        title: '设置已重置',
        message: '所有设置已恢复为默认值',
        duration: 3000
      })
    }
  }

  return (
    <>
      {/* 页面样式 */}
      <style jsx>{`
        .container {
          max-width: 1200px;
          margin: 0 auto;
        }

        .page-header {
          margin-bottom: 32px;
        }

        .page-title {
          font-size: 28px;
          font-weight: 700;
          margin-bottom: 8px;
          color: var(--text-primary);
        }

        .page-description {
          font-size: 16px;
          color: var(--text-secondary);
        }

        .settings-grid {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          gap: 24px;
          margin-bottom: 32px;
        }

        .settings-card {
          background: var(--bg-secondary);
          border-radius: 12px;
          padding: 24px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid var(--border-primary);
        }

        .card-header {
          display: flex;
          align-items: flex-start;
          margin-bottom: 20px;
          padding-bottom: 16px;
          border-bottom: 1px solid var(--border-primary);
        }

        .card-icon {
          width: 24px;
          height: 24px;
          margin-right: 12px;
          margin-top: 2px;
          fill: var(--theme-primary);
          flex-shrink: 0;
        }

        .card-title {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 4px;
          color: var(--text-primary);
        }

        .card-subtitle {
          font-size: 14px;
          color: var(--text-secondary);
          line-height: 1.4;
        }

        .form-group {
          margin-bottom: 20px;
        }

        .form-label {
          display: block;
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 6px;
          color: var(--text-tertiary);
        }

        .form-label.required::after {
          content: " *";
          color: #dc2626;
        }

        .form-select, .form-input, .form-textarea {
          width: 100%;
          padding: 10px 12px;
          border: 1px solid var(--border-secondary);
          border-radius: 6px;
          font-size: 14px;
          background: var(--bg-secondary);
          color: var(--text-primary);
          transition: border-color 0.2s;
          font-family: inherit;
        }

        .form-textarea {
          resize: vertical;
          min-height: 80px;
        }

        .form-select:focus, .form-input:focus, .form-textarea:focus {
          outline: none;
          border-color: var(--theme-primary);
          box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-hint {
          font-size: 12px;
          color: var(--text-secondary);
          margin-top: 4px;
        }

        .form-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
        }

        .test-section {
          margin-top: 20px;
          padding-top: 20px;
          border-top: 1px solid var(--border-primary);
        }

        .test-row {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .btn {
          padding: 8px 16px;
          border: 1px solid var(--border-secondary);
          border-radius: 6px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s;
          display: inline-flex;
          align-items: center;
          gap: 6px;
          background: transparent;
        }

        .btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .btn-secondary {
          background: var(--bg-secondary);
          color: var(--text-tertiary);
        }

        .btn-secondary:hover:not(:disabled) {
          border-color: var(--theme-primary);
          color: var(--theme-primary);
        }

        .btn-primary {
          background: var(--theme-primary);
          color: white;
          border-color: var(--theme-primary);
        }

        .btn-primary:hover:not(:disabled) {
          background: var(--theme-primary-hover);
        }

        .btn-danger {
          background: #dc2626;
          color: white;
          border-color: #dc2626;
        }

        .btn-danger:hover:not(:disabled) {
          background: #b91c1c;
        }

        .status-indicator {
          display: inline-flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 4px;
        }

        .status-success {
          background: #dcfce7;
          color: #166534;
        }

        .status-error {
          background: #fef2f2;
          color: #991b1b;
        }

        .spinner {
          width: 16px;
          height: 16px;
          border: 2px solid currentColor;
          border-top-color: transparent;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        .actions-section {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 24px;
          background: var(--bg-secondary);
          border-radius: 12px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid var(--border-primary);
        }

        .actions-left {
          display: flex;
          gap: 12px;
        }

        @keyframes spin {
          to { transform: rotate(360deg); }
        }

        @media (max-width: 1200px) {
          .settings-grid {
            grid-template-columns: 1fr 1fr;
          }
        }

        @media (max-width: 768px) {
          .settings-grid {
            grid-template-columns: 1fr;
          }

          .form-row {
            grid-template-columns: 1fr;
          }

          .actions-section {
            flex-direction: column;
            gap: 16px;
          }

          .actions-left {
            width: 100%;
            justify-content: center;
          }
        }
      `}</style>

      <div className="container">
        {/* 页面标题 */}
        <div className="page-header">
          <h1 className="page-title">系统设置</h1>
          <p className="page-description">配置文字转语音服务和大模型服务，确保系统正常运行</p>
        </div>

        {/* 设置内容 */}
        <div className="settings-grid">
          {/* TTS服务配置 */}
          <div className="settings-card">
            <div className="card-header">
              <svg className="card-icon" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
              <div>
                <h2 className="card-title">文字转语音服务 (TTS)</h2>
                <p className="card-subtitle">配置语音合成服务提供商和相关认证信息</p>
              </div>
            </div>

            {/* TTS服务选择 */}
            <div className="form-group">
              <label className="form-label required">服务提供商</label>
              <select 
                className="form-select" 
                value={tts.provider || ''}
                onChange={(e) => handleTTSProviderChange(e.target.value)}
              >
                <option value="">请选择TTS服务商</option>
                <option value="coze">Coze TTS</option>
                <option value="f5-tts">F5-TTS</option>
              </select>
              <div className="form-hint">使用Coze工作流提供的文字转语音服务</div>
            </div>

            {/* Coze TTS 配置 */}
            {tts.provider === 'coze' && (
              <>
                <div className="form-group">
                  <label className="form-label required">Workflow ID</label>
                  <input 
                    type="text"
                    className="form-input"
                    placeholder="7520141766219563047"
                    value={tts.model || ''}
                    onChange={(e) => updateTTSConfig({ model: e.target.value })}
                  />
                  <div className="form-hint">Coze工作流ID，用于标识TTS工作流</div>
                </div>

                <div className="form-group">
                  <label className="form-label required">Token</label>
                  <input 
                    type="password"
                    className="form-input"
                    placeholder="pat_xxx..."
                    value={tts.apiKey || ''}
                    onChange={(e) => updateTTSConfig({ apiKey: e.target.value })}
                  />
                  <div className="form-hint">您的Coze API访问令牌</div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">说话人ID</label>
                    <select 
                      className="form-select"
                      value={tts.voice || ''}
                      onChange={(e) => updateTTSConfig({ voice: e.target.value })}
                    >
                      <option value="">请选择说话人</option>
                      {ttsVoices.map((voice) => (
                        <option key={voice.id} value={voice.id}>
                          {voice.name}
                        </option>
                      ))}
                    </select>
                    <div className="form-hint">选择合成语音的说话人</div>
                  </div>

                  <div className="form-group">
                    <label className="form-label">语速</label>
                    <input 
                      type="number"
                      className="form-input"
                      placeholder="1.2"
                      min="0.5"
                      max="2.0"
                      step="0.1"
                      value={tts.speed || ''}
                      onChange={(e) => updateTTSConfig({ speed: parseFloat(e.target.value) || 1.2 })}
                    />
                    <div className="form-hint">语速倍率 (0.5-2.0)，默认1.2</div>
                  </div>
                </div>
              </>
            )}

            {/* F5-TTS 配置 */}
            {tts.provider === 'f5-tts' && (
              <>
                <div className="form-group">
                  <label className="form-label required">F5-TTS服务端点</label>
                  <input
                    type="url"
                    className="form-input"
                    placeholder="http://your-f5-tts-server:port/"
                    value={tts.f5TtsEndpoint || ''}
                    onChange={(e) => updateTTSConfig({ f5TtsEndpoint: e.target.value })}
                  />
                  <div className="form-hint">F5-TTS服务的访问地址</div>
                </div>

                {/* F5-TTS音色管理 */}
                <div className="form-group">
                  <label className="form-label">音色管理</label>
                  <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <F5TTSVoiceManager />
                  </div>
                  <div className="form-hint">管理F5-TTS音色，每个音色需要参考音频文件和对应文本</div>
                </div>
              </>
            )}

            {/* TTS测试 */}
            <div className="test-section">
              <div className="test-row">
                <button 
                  className="btn btn-secondary"
                  onClick={handleTestTTS}
                  disabled={isTestingTTS || !tts.provider ||
                    (tts.provider === 'coze' && (!tts.apiKey || !tts.model)) ||
                    (tts.provider === 'f5-tts' && !tts.f5TtsEndpoint)}
                >
                  {isTestingTTS ? (
                    <>
                      <div className="spinner" />
                      测试中...
                    </>
                  ) : (
                    '测试连接'
                  )}
                </button>

                {testResults.tts === 'success' && (
                  <div className="status-indicator status-success">
                    <svg width="12" height="12" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                    </svg>
                    连接成功
                  </div>
                )}

                {testResults.tts === 'error' && (
                  <div className="status-indicator status-error">
                    <svg width="12" height="12" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
                    </svg>
                    连接失败
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 大模型配置 */}
          <div className="settings-card">
            <div className="card-header">
              <svg className="card-icon" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <div>
                <h2 className="card-title">大模型服务 (LLM)</h2>
                <p className="card-subtitle">配置用于生成故事内容的大语言模型服务</p>
              </div>
            </div>

            {/* LLM服务选择 */}
            <div className="form-group">
              <label className="form-label required">服务提供商</label>
              <select 
                className="form-select" 
                value={llm.provider || ''}
                onChange={(e) => handleLLMProviderChange(e.target.value)}
              >
                <option value="">请选择大模型服务商</option>
                <option value="yunwu">云雾API</option>
              </select>
              <div className="form-hint">使用云雾API提供的大语言模型服务（根地址：https://yunwu.ai/）</div>
            </div>

            {/* 云雾API 配置 */}
            {llm.provider === 'yunwu' && (
              <>
                <div className="form-group">
                  <label className="form-label required">API Key</label>
                  <input 
                    type="password"
                    className="form-input"
                    placeholder="yk-..."
                    value={llm.apiKey || ''}
                    onChange={(e) => updateLLMConfig({ apiKey: e.target.value })}
                  />
                  <div className="form-hint">您的云雾API密钥</div>
                </div>

                <div className="form-group">
                  <label className="form-label">模型</label>
                  <select 
                    className="form-select"
                    value={llm.model || ''}
                    onChange={(e) => updateLLMConfig({ model: e.target.value })}
                  >
                    <option value="gpt-4o">gpt-4o</option>
                    <option value="claude-sonnet-4-20250514">claude-sonnet-4-20250514</option>
                    <option value="claude-opus-4-20250514">claude-opus-4-20250514</option>
                    <option value="gemini-2.5-pro">gemini-2.5-pro</option>
                  </select>
                  <div className="form-hint">选择要使用的大语言模型</div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">温度</label>
                    <input 
                      type="number"
                      className="form-input"
                      placeholder="0.7"
                      min="0"
                      max="2"
                      step="0.1"
                      value={llm.temperature || ''}
                      onChange={(e) => updateLLMConfig({ temperature: parseFloat(e.target.value) || 0.7 })}
                    />
                  </div>
                  
                  <div className="form-group">
                    <label className="form-label">最大Token数(max=10240)</label>
                    <input 
                      type="number"
                      className="form-input"
                      placeholder="2000"
                      min="1"
                      max="10240"
                      value={llm.maxTokens || ''}
                      onChange={(e) => updateLLMConfig({ maxTokens: parseInt(e.target.value) || 2000 })}
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-label">系统提示词</label>
                  <textarea 
                    className="form-textarea"
                    placeholder="你是一个专业的Reddit故事视频内容创作者..."
                    rows={3}
                    value={llm.systemPrompt || ''}
                    onChange={(e) => updateLLMConfig({ systemPrompt: e.target.value })}
                  />
                  <div className="form-hint">设置模型的角色和行为指导</div>
                </div>
              </>
            )}

            {/* LLM测试 */}
            <div className="test-section">
              <div className="test-row">
                <button 
                  className="btn btn-secondary"
                  onClick={handleTestLLM}
                  disabled={isTestingLLM || !llm.provider || !llm.apiKey}
                >
                  {isTestingLLM ? (
                    <>
                      <div className="spinner" />
                      测试中...
                    </>
                  ) : (
                    '测试连接'
                  )}
                </button>

                {testResults.llm === 'success' && (
                  <div className="status-indicator status-success">
                    <svg width="12" height="12" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                    </svg>
                    连接成功
                  </div>
                )}

                {testResults.llm === 'error' && (
                  <div className="status-indicator status-error">
                    <svg width="12" height="12" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
                    </svg>
                    连接失败
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 飞书多维表格配置 */}
          <div className="settings-card">
            <div className="card-header">
              <svg className="card-icon" viewBox="0 0 20 20" fill="currentColor">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
              <div>
                <h2 className="card-title">飞书多维表格</h2>
                <p className="card-subtitle">配置飞书多维表格集成，支持从表格导入文案和回写生成状态</p>
              </div>
            </div>

            <div className="form-group">
              <label className="form-label required">App ID</label>
              <input
                type="text"
                className="form-input"
                placeholder="cli_xxx..."
                value={feishu.appId}
                onChange={(e) => updateFeishuConfig({ appId: e.target.value })}
              />
              <div className="form-hint">飞书应用的App ID</div>
            </div>

            <div className="form-group">
              <label className="form-label required">App Secret</label>
              <input
                type="password"
                className="form-input"
                placeholder="xxx..."
                value={feishu.appSecret}
                onChange={(e) => updateFeishuConfig({ appSecret: e.target.value })}
              />
              <div className="form-hint">飞书应用的App Secret</div>
            </div>

            <div className="form-group">
              <label className="form-label required">App Token</label>
              <input
                type="text"
                className="form-input"
                placeholder="bascxxx..."
                value={feishu.appToken}
                onChange={(e) => updateFeishuConfig({ appToken: e.target.value })}
              />
              <div className="form-hint">多维表格的App Token</div>
            </div>

            <div className="form-group">
              <label className="form-label required">Table ID</label>
              <input
                type="text"
                className="form-input"
                placeholder="tblxxx..."
                value={feishu.tableId}
                onChange={(e) => updateFeishuConfig({ tableId: e.target.value })}
              />
              <div className="form-hint">多维表格的Table ID</div>
            </div>

            {/* 飞书连接测试 */}
            <div className="test-section">
              <div className="test-row">
                <button
                  className="btn btn-secondary"
                  onClick={handleTestFeishu}
                  disabled={isTestingFeishu || !feishu.appId || !feishu.appSecret || !feishu.appToken || !feishu.tableId}
                >
                  {isTestingFeishu ? (
                    <>
                      <div className="spinner" />
                      测试中...
                    </>
                  ) : (
                    '测试连接'
                  )}
                </button>

                {testResults.feishu === 'success' && (
                  <div className="status-indicator status-success">
                    <svg width="12" height="12" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                    </svg>
                    连接成功
                  </div>
                )}

                {testResults.feishu === 'error' && (
                  <div className="status-indicator status-error">
                    <svg width="12" height="12" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
                    </svg>
                    连接失败
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="actions-section">
          <div className="actions-left">
            <button 
              className="btn btn-danger"
              onClick={handleReset}
            >
              重置默认设置
            </button>
          </div>

          <button 
            className="btn btn-primary"
            onClick={handleSave}
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <div className="spinner" />
                保存中...
              </>
            ) : (
              '保存设置'
            )}
          </button>
        </div>
      </div>
    </>
  )
}
